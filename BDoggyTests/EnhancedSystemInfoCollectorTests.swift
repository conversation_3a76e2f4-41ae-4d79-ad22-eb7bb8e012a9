//
//  EnhancedSystemInfoCollectorTests.swift
//  BDoggyTests
//
//  Created by K4 on 2025/1/11.
//

@testable import BDoggy
import XCTest

final class EnhancedSystemInfoCollectorTests: XCTestCase {
    
    // MARK: - Setup & Teardown
    
    override func setUpWithError() throws {
        // 测试前的准备工作
        continueAfterFailure = false
    }
    
    override func tearDownWithError() throws {
        // 测试后的清理工作
    }
    
    // MARK: - getCompleteSystemInfo() 核心测试

    /// 测试 getCompleteSystemInfo() 方法能够成功返回完整的系统信息
    func testGetCompleteSystemInfoReturnsValidData() throws {
        // 执行方法
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        // 打印完整的系统信息结构
        NSLog("\n" + String(repeating: "=", count: 80))
        NSLog("🔍 完整系统信息输出 - testGetCompleteSystemInfoReturnsValidData")
        NSLog(String(repeating: "=", count: 80))
        NSLog("📊 总字段数: \(systemInfo.keys.count)")
        NSLog("\n📋 所有字段和值:")

        // 按字母顺序排序并打印所有字段
        for (key, value) in systemInfo.sorted(by: { $0.key < $1.key }) {
            let valueString = formatValueForPrint(value)
            NSLog("  \(key): \(valueString)")
        }
        NSLog(String(repeating: "=", count: 80) + "\n")

        // 验证返回的不是空字典
        XCTAssertFalse(systemInfo.isEmpty, "系统信息字典不应为空")

        // 验证包含基本的系统信息字段
        XCTAssertNotNil(systemInfo["device_model"], "应包含设备型号")
        XCTAssertNotNil(systemInfo["cpu_name"], "应包含CPU名称")
        XCTAssertNotNil(systemInfo["cpu_core_count"], "应包含CPU核心数")
        XCTAssertNotNil(systemInfo["total_memory_gb"], "应包含总内存")
        XCTAssertNotNil(systemInfo["total_disk_gb"], "应包含总磁盘空间")
        XCTAssertNotNil(systemInfo["macos_version"], "应包含macOS版本")
        XCTAssertNotNil(systemInfo["kernel_version"], "应包含内核版本")
        XCTAssertNotNil(systemInfo["system_serial"], "应包含系统序列号")
        XCTAssertNotNil(systemInfo["system_uptime"], "应包含系统运行时间")

        // 验证当前系统状态字段
        XCTAssertNotNil(systemInfo["current_cpu_usage"], "应包含当前CPU使用率")
        XCTAssertNotNil(systemInfo["current_memory_usage"], "应包含当前内存使用率")
        XCTAssertNotNil(systemInfo["current_disk_usage"], "应包含当前磁盘使用率")

        // 验证网络信息
        XCTAssertNotNil(systemInfo["network_bytes_in"], "应包含网络入站字节数")
        XCTAssertNotNil(systemInfo["network_bytes_out"], "应包含网络出站字节数")

        // 验证时间戳
        XCTAssertNotNil(systemInfo["collected_at"], "应包含收集时间戳")
        XCTAssertNotNil(systemInfo["collected_at_iso"], "应包含ISO格式时间戳")

        print("✅ 系统信息包含 \(systemInfo.keys.count) 个字段")
    }
    
    /// 测试返回数据的类型和格式正确性
    func testGetCompleteSystemInfoDataTypes() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        print("\n" + String(repeating: "=", count: 80))
        print("🔍 数据类型详细分析 - testGetCompleteSystemInfoDataTypes")
        print(String(repeating: "=", count: 80))

        // 验证字符串类型字段
        let stringFields = ["device_model", "cpu_name", "macos_version", "kernel_version", "system_serial", "collected_at_iso"]
        print("\n📝 字符串类型字段:")
        for field in stringFields {
            if let value = systemInfo[field] {
                let isString = value is String
                print("  \(field): \(isString ? "✅" : "❌") String - 值: \(value)")
                XCTAssertTrue(isString, "\(field)应为字符串类型")
            } else {
                print("  \(field): ❌ 缺失")
            }
        }

        // 验证数值类型字段
        let intFields = ["cpu_core_count"]
        print("\n🔢 整数类型字段:")
        for field in intFields {
            if let value = systemInfo[field] {
                let isInt = value is Int
                print("  \(field): \(isInt ? "✅" : "❌") Int - 值: \(value)")
                XCTAssertTrue(isInt, "\(field)应为整数类型")
            } else {
                print("  \(field): ❌ 缺失")
            }
        }

        let doubleFields = ["total_memory_gb", "total_disk_gb", "current_cpu_usage", "current_memory_usage", "current_disk_usage", "system_uptime", "collected_at"]
        print("\n📊 浮点数类型字段:")
        for field in doubleFields {
            if let value = systemInfo[field] {
                let isDouble = value is Double
                print("  \(field): \(isDouble ? "✅" : "❌") Double - 值: \(value)")
                XCTAssertTrue(isDouble, "\(field)应为Double类型")
            } else {
                print("  \(field): ❌ 缺失")
            }
        }

        // 验证网络字节数类型（可能是 Int64 或 UInt64）
        print("\n🌐 网络字节数类型:")
        let networkBytesIn = systemInfo["network_bytes_in"]
        let networkBytesOut = systemInfo["network_bytes_out"]

        if let bytesIn = networkBytesIn {
            let isValidType = bytesIn is UInt64 || bytesIn is Int64
            print("  network_bytes_in: \(isValidType ? "✅" : "❌") \(type(of: bytesIn)) - 值: \(bytesIn)")
            XCTAssertTrue(isValidType, "网络入站字节数应为UInt64或Int64类型")
        }

        if let bytesOut = networkBytesOut {
            let isValidType = bytesOut is UInt64 || bytesOut is Int64
            print("  network_bytes_out: \(isValidType ? "✅" : "❌") \(type(of: bytesOut)) - 值: \(bytesOut)")
            XCTAssertTrue(isValidType, "网络出站字节数应为UInt64或Int64类型")
        }

        // 验证Top进程数组
        print("\n🔄 Top进程信息:")
        if let topProcesses = systemInfo["top_processes"] as? [[String: Any]] {
            print("  top_processes: ✅ Array<Dictionary> - 进程数量: \(topProcesses.count)")
            XCTAssertTrue(topProcesses.count <= 5, "Top进程数量应不超过5个")

            if !topProcesses.isEmpty {
                print("  第一个进程详细信息:")
                let firstProcess = topProcesses[0]
                for (key, value) in firstProcess.sorted(by: { $0.key < $1.key }) {
                    print("    \(key): \(type(of: value)) - 值: \(value)")
                }

                XCTAssertNotNil(firstProcess["pid"], "进程应包含PID")
                XCTAssertNotNil(firstProcess["name"], "进程应包含名称")
                XCTAssertNotNil(firstProcess["cpu_usage"], "进程应包含CPU使用率")
                XCTAssertNotNil(firstProcess["memory_usage_mb"], "进程应包含内存使用量")

                // 验证进程字段的具体类型
                let pidValue = firstProcess["pid"]
                XCTAssertTrue(pidValue is Int32 || pidValue is Int, "进程PID应为整数类型，实际类型: \(type(of: pidValue))")
                XCTAssertTrue(firstProcess["name"] is String, "进程名称应为字符串类型")
                XCTAssertTrue(firstProcess["cpu_usage"] is Double, "进程CPU使用率应为Double类型")
                XCTAssertTrue(firstProcess["memory_usage_mb"] is Double, "进程内存使用量应为Double类型")
            }
        } else {
            print("  top_processes: ❌ 类型错误")
            XCTFail("top_processes 应为数组类型")
        }

        // 验证系统负载字典
        print("\n⚡ 系统负载信息:")
        if let loadAverage = systemInfo["load_average"] as? [String: Double] {
            print("  load_average: ✅ Dictionary<String, Double>")
            for (key, value) in loadAverage.sorted(by: { $0.key < $1.key }) {
                print("    \(key): \(value)")
            }
            XCTAssertNotNil(loadAverage["1min"], "应包含1分钟负载平均值")
            XCTAssertNotNil(loadAverage["5min"], "应包含5分钟负载平均值")
            XCTAssertNotNil(loadAverage["15min"], "应包含15分钟负载平均值")
        } else {
            print("  load_average: ❌ 类型错误")
            XCTFail("load_average 应为字典类型")
        }

        print(String(repeating: "=", count: 80) + "\n")
        print("✅ 所有数据类型验证通过")
    }
    
    /// 测试数据值的合理性范围
    func testGetCompleteSystemInfoDataRanges() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()
        
        // 验证CPU使用率范围 (0-100)
        if let cpuUsage = systemInfo["current_cpu_usage"] as? Double {
            XCTAssertGreaterThanOrEqual(cpuUsage, 0.0, "CPU使用率应大于等于0")
            XCTAssertLessThanOrEqual(cpuUsage, 100.0, "CPU使用率应小于等于100")
        }
        
        // 验证内存使用率范围 (0-100)
        if let memoryUsage = systemInfo["current_memory_usage"] as? Double {
            XCTAssertGreaterThanOrEqual(memoryUsage, 0.0, "内存使用率应大于等于0")
            XCTAssertLessThanOrEqual(memoryUsage, 100.0, "内存使用率应小于等于100")
        }
        
        // 验证磁盘使用率范围 (0-100)
        if let diskUsage = systemInfo["current_disk_usage"] as? Double {
            XCTAssertGreaterThanOrEqual(diskUsage, 0.0, "磁盘使用率应大于等于0")
            XCTAssertLessThanOrEqual(diskUsage, 100.0, "磁盘使用率应小于等于100")
        }
        
        // 验证CPU核心数合理性
        if let coreCount = systemInfo["cpu_core_count"] as? Int {
            XCTAssertGreaterThan(coreCount, 0, "CPU核心数应大于0")
            XCTAssertLessThanOrEqual(coreCount, 128, "CPU核心数应在合理范围内")
        }
        
        // 验证内存大小合理性
        if let totalMemory = systemInfo["total_memory_gb"] as? Double {
            XCTAssertGreaterThan(totalMemory, 0, "总内存应大于0")
            XCTAssertLessThan(totalMemory, 1024, "总内存应在合理范围内")
        }
        
        // 验证磁盘大小合理性
        if let totalDisk = systemInfo["total_disk_gb"] as? Double {
            XCTAssertGreaterThan(totalDisk, 0, "总磁盘空间应大于0")
            XCTAssertLessThan(totalDisk, 100000, "总磁盘空间应在合理范围内")
        }
        
        // 验证系统运行时间合理性
        if let uptime = systemInfo["system_uptime"] as? Double {
            XCTAssertGreaterThan(uptime, 0, "系统运行时间应大于0")
            XCTAssertLessThan(uptime, 365 * 24 * 3600, "系统运行时间应在合理范围内")
        }
        
        // 验证时间戳合理性
        if let timestamp = systemInfo["collected_at"] as? Double {
            let currentTime = Date().timeIntervalSince1970
            XCTAssertLessThanOrEqual(abs(timestamp - currentTime), 5.0, "时间戳应接近当前时间")
        }
        
        print("✅ 所有数据范围验证通过")
    }
    
    /// 测试方法执行性能
    func testGetCompleteSystemInfoPerformance() throws {
        print("\n" + String(repeating: "=", count: 80))
        print("⚡ 性能测试 - testGetCompleteSystemInfoPerformance")
        print(String(repeating: "=", count: 80))

        // 性能测试：方法应在合理时间内完成
        print("🔄 开始性能基准测试...")
        measure {
            _ = EnhancedSystemInfoCollector.getCompleteSystemInfo()
        }

        // 单次执行时间测试
        print("🔄 单次执行时间测试...")
        let startTime = Date()
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()
        let executionTime = Date().timeIntervalSince(startTime)

        // 打印关键性能指标
        print("📊 性能指标:")
        print("  执行时间: \(String(format: "%.3f", executionTime))秒")
        print("  数据字段数: \(systemInfo.keys.count)")
        print("  数据收集效率: \(String(format: "%.1f", Double(systemInfo.keys.count) / executionTime)) 字段/秒")

        // 打印关键系统信息摘要
        print("\n📋 系统信息摘要:")
        if let deviceModel = systemInfo["device_model"] as? String {
            print("  设备型号: \(deviceModel)")
        }
        if let cpuName = systemInfo["cpu_name"] as? String {
            print("  CPU: \(cpuName)")
        }
        if let coreCount = systemInfo["cpu_core_count"] as? Int {
            print("  CPU核心数: \(coreCount)")
        }
        if let totalMemory = systemInfo["total_memory_gb"] as? Double {
            print("  总内存: \(String(format: "%.1f", totalMemory)) GB")
        }
        if let cpuUsage = systemInfo["current_cpu_usage"] as? Double {
            print("  当前CPU使用率: \(String(format: "%.1f", cpuUsage))%")
        }
        if let memoryUsage = systemInfo["current_memory_usage"] as? Double {
            print("  当前内存使用率: \(String(format: "%.1f", memoryUsage))%")
        }

        // 验证执行时间在合理范围内（应小于5秒）
        XCTAssertLessThan(executionTime, 5.0, "方法执行时间应小于5秒")

        print(String(repeating: "=", count: 80) + "\n")
        print("✅ 方法执行时间: \(String(format: "%.3f", executionTime))秒")
    }
    
    /// 测试方法不会抛出异常或崩溃
    func testGetCompleteSystemInfoStability() throws {
        // 多次调用测试稳定性
        for i in 1...10 {
            XCTAssertNoThrow({
                let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()
                XCTAssertFalse(systemInfo.isEmpty, "第\(i)次调用应返回有效数据")
            }, "第\(i)次调用不应抛出异常")
        }
        
        print("✅ 稳定性测试通过：10次连续调用无异常")
    }
    
    /// 测试并发调用安全性
    func testGetCompleteSystemInfoConcurrency() throws {
        let expectation = XCTestExpectation(description: "并发调用完成")
        let concurrentQueue = DispatchQueue(label: "test.concurrent", attributes: .concurrent)
        var results: [Int: [String: Any]] = [:]
        let resultsQueue = DispatchQueue(label: "test.results")
        
        // 并发执行5次
        for i in 0..<5 {
            concurrentQueue.async {
                let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()
                
                resultsQueue.async {
                    results[i] = systemInfo
                    
                    if results.count == 5 {
                        expectation.fulfill()
                    }
                }
            }
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证所有并发调用都成功返回数据
        XCTAssertEqual(results.count, 5, "应有5个并发调用结果")
        
        for (index, systemInfo) in results {
            XCTAssertFalse(systemInfo.isEmpty, "并发调用\(index)应返回有效数据")
            XCTAssertNotNil(systemInfo["device_model"], "并发调用\(index)应包含设备型号")
        }
        
        print("✅ 并发调用测试通过：5次并发调用均成功")
    }

    // MARK: - 特定字段详细测试

    /// 测试电池信息字段（如果设备有电池）
    func testBatteryInfoFields() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        print("\n" + String(repeating: "=", count: 80))
        print("🔋 电池信息测试 - testBatteryInfoFields")
        print(String(repeating: "=", count: 80))

        // 电池信息可能存在也可能不存在（取决于设备类型）
        // 首先检查是否有任何电池相关信息
        let hasBatteryLevel = systemInfo["battery_level"] != nil
        let hasBatteryHealth = systemInfo["battery_health"] != nil
        let hasBatteryCycleCount = systemInfo["battery_cycle_count"] != nil
        let hasPowerSource = systemInfo["power_source"] != nil

        print("🔍 电池相关字段检测:")
        print("  battery_level: \(hasBatteryLevel ? "✅ 存在" : "❌ 不存在")")
        print("  battery_health: \(hasBatteryHealth ? "✅ 存在" : "❌ 不存在")")
        print("  battery_cycle_count: \(hasBatteryCycleCount ? "✅ 存在" : "❌ 不存在")")
        print("  power_source: \(hasPowerSource ? "✅ 存在" : "❌ 不存在")")

        if hasBatteryLevel || hasBatteryHealth || hasBatteryCycleCount || hasPowerSource {
            print("\n🔋 检测到电池信息，进行详细验证...")

            // 如果有电池电量信息
            if let batteryLevel = systemInfo["battery_level"] as? Int {
                print("  电池电量: \(batteryLevel)% (类型: \(type(of: batteryLevel)))")
                XCTAssertGreaterThanOrEqual(batteryLevel, 0, "电池电量应大于等于0")
                XCTAssertLessThanOrEqual(batteryLevel, 100, "电池电量应小于等于100")
            } else {
                print("  电池电量: 无数据")
            }

            // 如果有电池健康度信息
            if let batteryHealth = systemInfo["battery_health"] as? Double {
                print("  电池健康度: \(String(format: "%.1f", batteryHealth))% (类型: \(type(of: batteryHealth)))")
                XCTAssertGreaterThanOrEqual(batteryHealth, 0.0, "电池健康度应大于等于0")
                XCTAssertLessThanOrEqual(batteryHealth, 100.0, "电池健康度应小于等于100")
            } else {
                print("  电池健康度: 无数据")
            }

            // 如果有电池循环次数信息
            if let cycleCount = systemInfo["battery_cycle_count"] as? Int {
                print("  电池循环次数: \(cycleCount) (类型: \(type(of: cycleCount)))")
                XCTAssertGreaterThanOrEqual(cycleCount, 0, "电池循环次数应大于等于0")
            } else {
                print("  电池循环次数: 无数据")
            }

            // 如果有电源类型信息
            if let powerSource = systemInfo["power_source"] as? String {
                print("  电源类型: \(powerSource) (类型: \(type(of: powerSource)))")
                XCTAssertFalse(powerSource.isEmpty, "电源类型不应为空")
            } else {
                print("  电源类型: 无数据")
            }

            print("✅ 电池信息验证通过")
        } else {
            print("\nℹ️ 当前设备无电池信息（可能是台式机或电池信息不可用）")
            // 这不是错误，只是信息性的
        }

        print(String(repeating: "=", count: 80) + "\n")
    }

    /// 测试温度信息字段
    func testTemperatureFields() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        // CPU温度（可能获取不到）
        if let cpuTemp = systemInfo["current_cpu_temperature"] as? Double {
            XCTAssertGreaterThan(cpuTemp, 0, "CPU温度应大于0")
            XCTAssertLessThan(cpuTemp, 150, "CPU温度应在合理范围内")
            print("✅ CPU温度: \(cpuTemp)°C")
        } else {
            print("ℹ️ 无法获取CPU温度信息")
        }

        // GPU温度（可能获取不到）
        if let gpuTemp = systemInfo["gpu_temperature"] as? Double {
            XCTAssertGreaterThan(gpuTemp, 0, "GPU温度应大于0")
            XCTAssertLessThan(gpuTemp, 150, "GPU温度应在合理范围内")
            print("✅ GPU温度: \(gpuTemp)°C")
        } else {
            print("ℹ️ 无法获取GPU温度信息")
        }

        // 风扇转速（可能获取不到）
        if let fanSpeed = systemInfo["fan_speed"] as? Int {
            XCTAssertGreaterThanOrEqual(fanSpeed, 0, "风扇转速应大于等于0")
            XCTAssertLessThan(fanSpeed, 10000, "风扇转速应在合理范围内")
            print("✅ 风扇转速: \(fanSpeed) RPM")
        } else {
            print("ℹ️ 无法获取风扇转速信息")
        }
    }

    /// 测试活跃应用信息
    func testActiveApplicationInfo() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        // 活跃应用信息应该存在
        if let activeAppName = systemInfo["active_app_name"] as? String {
            XCTAssertFalse(activeAppName.isEmpty, "活跃应用名称不应为空")
            print("✅ 当前活跃应用: \(activeAppName)")
        }

        if let activeAppBundleID = systemInfo["active_app_bundle_id"] as? String {
            XCTAssertFalse(activeAppBundleID.isEmpty, "活跃应用Bundle ID不应为空")
            print("✅ 活跃应用Bundle ID: \(activeAppBundleID)")
        }
    }

    /// 测试系统负载信息
    func testSystemLoadInfo() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        print("\n" + String(repeating: "=", count: 80))
        print("⚡ 系统负载信息测试 - testSystemLoadInfo")
        print(String(repeating: "=", count: 80))

        print("🔍 系统负载分析:")
        if let loadAverage = systemInfo["load_average"] as? [String: Double] {
            print("  load_average: ✅ Dictionary<String, Double>")
            print("  负载详情:")

            // 按时间顺序显示负载信息
            let timeKeys = ["1min", "5min", "15min"]
            for key in timeKeys {
                if let value = loadAverage[key] {
                    let status = getLoadStatus(value)
                    print("    \(key): \(String(format: "%.2f", value)) \(status)")
                } else {
                    print("    \(key): 无数据")
                }
            }

            // 显示所有负载相关字段
            print("  所有负载字段:")
            for (key, value) in loadAverage.sorted(by: { $0.key < $1.key }) {
                print("    \(key): \(String(format: "%.3f", value))")
            }

            // 验证必需的负载字段
            XCTAssertNotNil(loadAverage["1min"], "应包含1分钟负载平均值")
            XCTAssertNotNil(loadAverage["5min"], "应包含5分钟负载平均值")
            XCTAssertNotNil(loadAverage["15min"], "应包含15分钟负载平均值")

            // 验证负载值的合理性
            for (key, value) in loadAverage {
                XCTAssertGreaterThanOrEqual(value, 0.0, "\(key)负载平均值应大于等于0")
                XCTAssertLessThan(value, 1000.0, "\(key)负载平均值应在合理范围内")
            }

            // 分析负载趋势
            if let load1 = loadAverage["1min"],
               let load5 = loadAverage["5min"],
               let load15 = loadAverage["15min"] {
                print("\n📈 负载趋势分析:")
                if load1 > load5 && load5 > load15 {
                    print("  趋势: 负载上升 📈")
                } else if load1 < load5 && load5 < load15 {
                    print("  趋势: 负载下降 📉")
                } else {
                    print("  趋势: 负载稳定 ➡️")
                }

                let avgLoad = (load1 + load5 + load15) / 3.0
                print("  平均负载: \(String(format: "%.2f", avgLoad))")
            }

        } else {
            print("  load_average: ❌ 缺失或类型错误")
            XCTFail("应包含系统负载信息")
        }

        print(String(repeating: "=", count: 80) + "\n")
        print("✅ 系统负载信息验证完成")
    }

    /// 获取负载状态描述
    private func getLoadStatus(_ load: Double) -> String {
        switch load {
        case 0..<1.0:
            return "🟢 低负载"
        case 1.0..<2.0:
            return "🟡 中等负载"
        case 2.0..<4.0:
            return "🟠 高负载"
        default:
            return "🔴 极高负载"
        }
    }

    /// 测试网络接口信息
    func testNetworkInterfaceInfo() throws {
        let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        print("\n" + String(repeating: "=", count: 80))
        print("🌐 网络接口信息测试 - testNetworkInterfaceInfo")
        print(String(repeating: "=", count: 80))

        // 验证网络字节数
        print("📊 网络流量统计:")
        if let bytesIn = systemInfo["network_bytes_in"] {
            print("  入站字节数: \(bytesIn) (\(type(of: bytesIn)))")
            if let bytesInValue = bytesIn as? UInt64 {
                print("    格式化显示: \(formatBytes(Int64(bytesInValue)))")
                XCTAssertGreaterThanOrEqual(bytesInValue, 0, "网络入站字节数应大于等于0")
            } else if let bytesInValue = bytesIn as? Int64 {
                print("    格式化显示: \(formatBytes(bytesInValue))")
                XCTAssertGreaterThanOrEqual(bytesInValue, 0, "网络入站字节数应大于等于0")
            }
        } else {
            print("  入站字节数: 无数据")
        }

        if let bytesOut = systemInfo["network_bytes_out"] {
            print("  出站字节数: \(bytesOut) (\(type(of: bytesOut)))")
            if let bytesOutValue = bytesOut as? UInt64 {
                print("    格式化显示: \(formatBytes(Int64(bytesOutValue)))")
                XCTAssertGreaterThanOrEqual(bytesOutValue, 0, "网络出站字节数应大于等于0")
            } else if let bytesOutValue = bytesOut as? Int64 {
                print("    格式化显示: \(formatBytes(bytesOutValue))")
                XCTAssertGreaterThanOrEqual(bytesOutValue, 0, "网络出站字节数应大于等于0")
            }
        } else {
            print("  出站字节数: 无数据")
        }

        // 验证网络接口名称
        print("\n🔌 网络接口信息:")
        if let interfaceName = systemInfo["network_interface"] as? String {
            print("  接口名称: \(interfaceName) (\(type(of: interfaceName)))")
            XCTAssertFalse(interfaceName.isEmpty, "网络接口名称不应为空")
        } else {
            print("  接口名称: 无数据")
        }

        // 打印其他可能的网络相关信息
        print("\n🔍 其他网络相关字段:")
        let networkRelatedFields = systemInfo.filter { key, _ in
            key.lowercased().contains("network") || key.lowercased().contains("interface") || key.lowercased().contains("ip")
        }

        if networkRelatedFields.isEmpty {
            print("  无其他网络相关字段")
        } else {
            for (key, value) in networkRelatedFields.sorted(by: { $0.key < $1.key }) {
                print("  \(key): \(formatValueForPrint(value))")
            }
        }

        print(String(repeating: "=", count: 80) + "\n")
        print("✅ 网络接口信息验证完成")
    }

    // MARK: - 辅助方法

    /// 格式化字节数显示
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }

    /// 格式化值用于打印显示
    private func formatValueForPrint(_ value: Any) -> String {
        switch value {
        case let stringValue as String:
            return "\"\(stringValue)\" (String)"
        case let intValue as Int:
            return "\(intValue) (Int)"
        case let int32Value as Int32:
            return "\(int32Value) (Int32)"
        case let int64Value as Int64:
            return "\(int64Value) (Int64)"
        case let uint64Value as UInt64:
            return "\(uint64Value) (UInt64)"
        case let doubleValue as Double:
            return "\(String(format: "%.3f", doubleValue)) (Double)"
        case let floatValue as Float:
            return "\(String(format: "%.3f", floatValue)) (Float)"
        case let boolValue as Bool:
            return "\(boolValue) (Bool)"
        case let arrayValue as [Any]:
            return "Array[\(arrayValue.count) items] (\(type(of: arrayValue)))"
        case let dictValue as [String: Any]:
            return "Dictionary[\(dictValue.keys.count) keys] (\(type(of: dictValue)))"
        default:
            return "\(value) (\(type(of: value)))"
        }
    }

    // MARK: - 边界条件和错误处理测试

    /// 测试在系统资源紧张时的行为
    func testSystemUnderStress() throws {
        // 这个测试模拟系统在高负载下的行为
        // 注意：这是一个轻量级的压力测试，不会对系统造成实际压力

        let expectation = XCTestExpectation(description: "压力测试完成")
        let stressQueue = DispatchQueue(label: "stress.test", attributes: .concurrent)
        var results: [[String: Any]] = []
        let resultsQueue = DispatchQueue(label: "stress.results")

        // 快速连续调用多次
        for _ in 0..<20 {
            stressQueue.async {
                let systemInfo = EnhancedSystemInfoCollector.getCompleteSystemInfo()

                resultsQueue.async {
                    results.append(systemInfo)

                    if results.count == 20 {
                        expectation.fulfill()
                    }
                }
            }
        }

        wait(for: [expectation], timeout: 30.0)

        // 验证所有调用都成功
        XCTAssertEqual(results.count, 20, "应有20个压力测试结果")

        for (index, systemInfo) in results.enumerated() {
            XCTAssertFalse(systemInfo.isEmpty, "压力测试第\(index+1)次调用应返回有效数据")
            XCTAssertNotNil(systemInfo["device_model"], "压力测试第\(index+1)次调用应包含设备型号")
        }

        print("✅ 压力测试通过：20次快速连续调用均成功")
    }

    /// 测试数据一致性
    func testDataConsistency() throws {
        // 连续两次调用，某些静态信息应该保持一致
        let systemInfo1 = EnhancedSystemInfoCollector.getCompleteSystemInfo()
        Thread.sleep(forTimeInterval: 1.0) // 等待1秒
        let systemInfo2 = EnhancedSystemInfoCollector.getCompleteSystemInfo()

        // 静态信息应该保持一致
        if let deviceModel1 = systemInfo1["device_model"] as? String,
           let deviceModel2 = systemInfo2["device_model"] as? String {
            XCTAssertEqual(deviceModel1, deviceModel2, "设备型号应保持一致")
        }

        if let cpuName1 = systemInfo1["cpu_name"] as? String,
           let cpuName2 = systemInfo2["cpu_name"] as? String {
            XCTAssertEqual(cpuName1, cpuName2, "CPU名称应保持一致")
        }

        if let coreCount1 = systemInfo1["cpu_core_count"] as? Int,
           let coreCount2 = systemInfo2["cpu_core_count"] as? Int {
            XCTAssertEqual(coreCount1, coreCount2, "CPU核心数应保持一致")
        }

        if let totalMemory1 = systemInfo1["total_memory_gb"] as? Double,
           let totalMemory2 = systemInfo2["total_memory_gb"] as? Double {
            XCTAssertEqual(totalMemory1, totalMemory2, accuracy: 0.1, "总内存应保持一致")
        }

        if let macOSVersion1 = systemInfo1["macos_version"] as? String,
           let macOSVersion2 = systemInfo2["macos_version"] as? String {
            XCTAssertEqual(macOSVersion1, macOSVersion2, "macOS版本应保持一致")
        }

        if let serial1 = systemInfo1["system_serial"] as? String,
           let serial2 = systemInfo2["system_serial"] as? String {
            XCTAssertEqual(serial1, serial2, "系统序列号应保持一致")
        }

        // 动态信息可能会变化，但应该在合理范围内
        if let timestamp1 = systemInfo1["collected_at"] as? Double,
           let timestamp2 = systemInfo2["collected_at"] as? Double {
            let timeDiff = abs(timestamp2 - timestamp1)
            XCTAssertGreaterThan(timeDiff, 0.5, "时间戳应有差异")
            XCTAssertLessThan(timeDiff, 5.0, "时间戳差异应在合理范围内")
        }

        // 验证两次调用都返回了有效数据
        XCTAssertFalse(systemInfo1.isEmpty, "第一次调用应返回有效数据")
        XCTAssertFalse(systemInfo2.isEmpty, "第二次调用应返回有效数据")

        print("✅ 数据一致性测试通过")
    }
}
