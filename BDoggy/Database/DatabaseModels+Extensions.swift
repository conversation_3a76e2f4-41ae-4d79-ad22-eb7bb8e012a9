//
//  DatabaseModels+Extensions.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库相关的模型和结构体定义
//

import Foundation

// MARK: - 数据库统计信息模型

/**
 * 数据库统计信息
 * 
 * 包含数据库的基本统计信息，用于监控和分析：
 * - 各表的记录数量
 * - 数据库文件大小
 * - 数据时间跨度
 * - 健康状态指标
 */
struct DatabaseStats {
    /// 各表的记录数量统计
    var tableCounts: [String: Int] = [:]
    
    /// 数据库文件大小（字节）
    var databaseSizeBytes: Int64 = 0
    
    /// 最早数据的时间
    var earliestDataTime: Date?
    
    /// 最新数据的时间
    var latestDataTime: Date?
    
    /// 数据库健康状态
    var isHealthy: Bool = true
    
    /// 最后更新时间
    var lastUpdated: Date = Date()
    
    /**
     * 计算总记录数
     */
    var totalRecords: Int {
        return tableCounts.values.reduce(0, +)
    }
    
    /**
     * 格式化的数据库大小
     */
    var formattedSize: String {
        return ByteCountFormatter.string(fromByteCount: databaseSizeBytes, countStyle: .file)
    }
    
    /**
     * 数据时间跨度（天数）
     */
    var dataSpanDays: Int? {
        guard let earliest = earliestDataTime,
              let latest = latestDataTime else { return nil }
        return Calendar.current.dateComponents([.day], from: earliest, to: latest).day
    }
}

// MARK: - 数据库大小信息模型

/**
 * 数据库大小详细信息
 * 
 * 提供更详细的数据库存储分析，包括：
 * - 总文件大小
 * - 各表的大小统计
 * - 索引大小信息
 */
struct DatabaseSizeInfo: Codable {
    /// 数据库文件总大小（字节）
    let totalFileSizeBytes: Int
    
    /// 各表的大小统计
    let tableStats: [TableSizeInfo]
    
    /// 最后更新时间
    let lastUpdated: Date
    
    /**
     * 格式化的总大小
     */
    var formattedTotalSize: String {
        return ByteCountFormatter.string(fromByteCount: Int64(totalFileSizeBytes), countStyle: .file)
    }
    
    /**
     * 最大的表信息
     */
    var largestTable: TableSizeInfo? {
        return tableStats.max { $0.estimatedSizeBytes < $1.estimatedSizeBytes }
    }
}

/**
 * 表大小信息
 */
struct TableSizeInfo: Codable {
    /// 表名
    let tableName: String
    
    /// 记录数量
    let recordCount: Int
    
    /// 估算的大小（字节）
    let estimatedSizeBytes: Int
    
    /// 索引大小（字节）
    let indexSizeBytes: Int?
    
    /**
     * 格式化的大小
     */
    var formattedSize: String {
        return ByteCountFormatter.string(fromByteCount: Int64(estimatedSizeBytes), countStyle: .file)
    }
    
    /**
     * 平均记录大小
     */
    var averageRecordSize: Double {
        guard recordCount > 0 else { return 0 }
        return Double(estimatedSizeBytes) / Double(recordCount)
    }
}

// MARK: - 数据库健康报告模型

/**
 * 数据库健康检查报告
 * 
 * 包含全面的数据库健康状态信息：
 * - 完整性检查结果
 * - 性能指标
 * - 问题和警告列表
 * - 改进建议
 */
struct DatabaseHealthReport {
    /// 完整性检查结果
    var integrityCheck: Bool = false
    
    /// 连接状态
    var connectionStatus: Bool = false
    
    /// 数据库大小（MB）
    var databaseSizeMB: Double = 0
    
    /// 可用磁盘空间（MB）
    var availableDiskSpaceMB: Double = 0
    
    /// 查询性能评分（0-100）
    var queryPerformanceScore: Int = 0
    
    /// 发现的问题列表
    var issues: [String] = []
    
    /// 警告列表
    var warnings: [String] = []
    
    /// 改进建议
    var recommendations: [String] = []
    
    /// 检查时间
    var checkTime: Date = Date()
    
    /**
     * 总体健康评分（0-100）
     */
    var overallHealthScore: Int {
        var score = 100
        
        // 完整性检查失败扣50分
        if !integrityCheck {
            score -= 50
        }
        
        // 连接问题扣30分
        if !connectionStatus {
            score -= 30
        }
        
        // 每个问题扣10分
        score -= issues.count * 10
        
        // 每个警告扣5分
        score -= warnings.count * 5
        
        // 查询性能影响
        score = min(score, queryPerformanceScore)
        
        return max(0, score)
    }
    
    /**
     * 健康状态描述
     */
    var healthDescription: String {
        let score = overallHealthScore
        switch score {
        case 90...100:
            return "优秀"
        case 70..<90:
            return "良好"
        case 50..<70:
            return "一般"
        case 30..<50:
            return "较差"
        default:
            return "严重"
        }
    }
    
    /**
     * 是否需要立即处理
     */
    var requiresImmediateAction: Bool {
        return overallHealthScore < 50 || !integrityCheck || !connectionStatus
    }
}

// MARK: - 查询性能统计模型

/**
 * 查询性能统计信息
 * 
 * 用于监控和分析数据库查询性能：
 * - 执行时间统计
 * - 查询频率分析
 * - 性能趋势跟踪
 */
struct QueryPerformanceStats {
    /// 查询类型或标识
    let queryIdentifier: String
    
    /// 执行次数
    var executionCount: Int = 0
    
    /// 总执行时间（毫秒）
    var totalExecutionTimeMs: Double = 0
    
    /// 最小执行时间（毫秒）
    var minExecutionTimeMs: Double = Double.greatestFiniteMagnitude
    
    /// 最大执行时间（毫秒）
    var maxExecutionTimeMs: Double = 0
    
    /// 最后执行时间
    var lastExecutionTime: Date?
    
    /**
     * 平均执行时间（毫秒）
     */
    var averageExecutionTimeMs: Double {
        guard executionCount > 0 else { return 0 }
        return totalExecutionTimeMs / Double(executionCount)
    }
    
    /**
     * 性能评级
     */
    var performanceRating: String {
        let avgTime = averageExecutionTimeMs
        switch avgTime {
        case 0..<10:
            return "优秀"
        case 10..<50:
            return "良好"
        case 50..<200:
            return "一般"
        case 200..<1000:
            return "较慢"
        default:
            return "很慢"
        }
    }
    
    /**
     * 添加新的执行记录
     */
    mutating func addExecution(timeMs: Double) {
        executionCount += 1
        totalExecutionTimeMs += timeMs
        minExecutionTimeMs = min(minExecutionTimeMs, timeMs)
        maxExecutionTimeMs = max(maxExecutionTimeMs, timeMs)
        lastExecutionTime = Date()
    }
}

// MARK: - 系统性能概览模型

/**
 * 系统性能概览
 * 
 * 聚合的系统性能数据，用于快速了解系统状态
 */
struct SystemPerformanceOverview {
    /// 时间戳
    let timestamp: Date
    
    /// CPU使用率（百分比）
    let cpuUsagePercent: Double
    
    /// 内存使用率（百分比）
    let memoryUsagePercent: Double
    
    /// 磁盘使用率（百分比）
    let diskUsagePercent: Double?
    
    /// 电池电量（百分比）
    let batteryLevelPercent: Double?
    
    /// CPU温度（摄氏度）
    let cpuTemperatureCelsius: Double?
    
    /// 活跃应用数量
    let activeAppCount: Int?
    
    /// 网络活动状态
    let networkActivityLevel: String?
    
    /**
     * 系统负载评级
     */
    var systemLoadRating: String {
        let avgUsage = (cpuUsagePercent + memoryUsagePercent) / 2
        switch avgUsage {
        case 0..<30:
            return "轻度"
        case 30..<60:
            return "中度"
        case 60..<80:
            return "重度"
        default:
            return "过载"
        }
    }
}
