//
//  DatabaseModels+Extensions.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库相关的模型和结构体定义
//

import Foundation

// MARK: - 数据库统计信息模型

/**
 * 数据库统计信息
 *
 * 包含数据库的基本统计信息，用于监控和分析：
 * - 各表的记录数量
 * - 数据库文件大小
 * - 数据时间跨度
 * - 健康状态指标
 */
struct DatabaseStats {
    /// 各表的记录数量统计
    var tableCounts: [String: Int] = [:]
    
    /// 数据库文件大小（字节）
    var databaseSizeBytes: Int64 = 0
    
    /// 最早数据的时间
    var earliestDataTime: Date?
    
    /// 最新数据的时间
    var latestDataTime: Date?
    
    /// 数据库健康状态
    var isHealthy: Bool = true
    
    /// 最后更新时间
    var lastUpdated: Date = .init()
    
    /**
     * 计算总记录数
     */
    var totalRecords: Int {
        return tableCounts.values.reduce(0, +)
    }
    
    /**
     * 格式化的数据库大小
     */
    var formattedSize: String {
        return ByteCountFormatter.string(fromByteCount: databaseSizeBytes, countStyle: .file)
    }
    
    /**
     * 数据时间跨度（天数）
     */
    var dataSpanDays: Int? {
        guard let earliest = earliestDataTime,
              let latest = latestDataTime else { return nil }
        return Calendar.current.dateComponents([.day], from: earliest, to: latest).day
    }
}

// MARK: - 数据库大小信息模型

/**
 * 数据库大小详细信息
 *
 * 提供更详细的数据库存储分析，包括：
 * - 总文件大小
 * - 各表的大小统计
 * - 索引大小信息
 */
struct DatabaseSizeInfo: Codable {
    /// 数据库文件总大小（字节）
    let totalFileSizeBytes: Int
    
    /// 各表的大小统计
    let tableStats: [TableSizeInfo]
    
    /// 最后更新时间
    let lastUpdated: Date
    
    /**
     * 格式化的总大小
     */
    var formattedTotalSize: String {
        return ByteCountFormatter.string(fromByteCount: Int64(totalFileSizeBytes), countStyle: .file)
    }
    
    /**
     * 最大的表信息
     */
    var largestTable: TableSizeInfo? {
        return tableStats.max { $0.estimatedSizeBytes < $1.estimatedSizeBytes }
    }
}

/**
 * 表大小信息
 */
struct TableSizeInfo: Codable {
    /// 表名
    let tableName: String
    
    /// 记录数量
    let recordCount: Int
    
    /// 估算的大小（字节）
    let estimatedSizeBytes: Int
    
    /// 索引大小（字节）
    let indexSizeBytes: Int?
    
    /**
     * 格式化的大小
     */
    var formattedSize: String {
        return ByteCountFormatter.string(fromByteCount: Int64(estimatedSizeBytes), countStyle: .file)
    }
    
    /**
     * 平均记录大小
     */
    var averageRecordSize: Double {
        guard recordCount > 0 else { return 0 }
        return Double(estimatedSizeBytes) / Double(recordCount)
    }
}

// MARK: - 数据库健康报告模型

/**
 * 数据库健康检查报告
 *
 * 包含全面的数据库健康状态信息：
 * - 完整性检查结果
 * - 性能指标
 * - 问题和警告列表
 * - 改进建议
 */
struct DatabaseHealthReport {
    /// 完整性检查结果
    var integrityCheck: Bool = false
    
    /// 连接状态
    var connectionStatus: Bool = false
    
    /// 数据库大小（MB）
    var databaseSizeMB: Double = 0
    
    /// 可用磁盘空间（MB）
    var availableDiskSpaceMB: Double = 0
    
    /// 查询性能评分（0-100）
    var queryPerformanceScore: Int = 0
    
    /// 发现的问题列表
    var issues: [String] = []
    
    /// 警告列表
    var warnings: [String] = []
    
    /// 改进建议
    var recommendations: [String] = []
    
    /// 检查时间
    var checkTime: Date = .init()
    
    /**
     * 总体健康评分（0-100）
     */
    var overallHealthScore: Int {
        var score = 100
        
        // 完整性检查失败扣50分
        if !integrityCheck {
            score -= 50
        }
        
        // 连接问题扣30分
        if !connectionStatus {
            score -= 30
        }
        
        // 每个问题扣10分
        score -= issues.count * 10
        
        // 每个警告扣5分
        score -= warnings.count * 5
        
        // 查询性能影响
        score = min(score, queryPerformanceScore)
        
        return max(0, score)
    }
    
    /**
     * 健康状态描述
     */
    var healthDescription: String {
        let score = overallHealthScore
        switch score {
        case 90 ... 100:
            return "优秀"
        case 70..<90:
            return "良好"
        case 50..<70:
            return "一般"
        case 30..<50:
            return "较差"
        default:
            return "严重"
        }
    }
    
    /**
     * 是否需要立即处理
     */
    var requiresImmediateAction: Bool {
        return overallHealthScore < 50 || !integrityCheck || !connectionStatus
    }
}

// MARK: - 系统性能概览模型

/**
 * 系统性能概览
 *
 * 聚合的系统性能数据，用于快速了解系统状态
 */
struct SystemPerformanceOverview: Codable, Identifiable {
    let id = UUID()
    let timestamp: Date
    let cpuUsage: Double?           // 0.0-100.0
    let memoryUsage: Double?        // 0.0-100.0
    let diskUsage: Double?          // 0.0-100.0
    let batteryLevel: Int?          // 0-100
    let cpuTemperature: Double?
    let thermalState: String?

    /// 系统健康评分 (0-100)
    var healthScore: Double {
        var score = 100.0

        if let cpu = cpuUsage, cpu > 80 {
            score -= (cpu - 80) * 2
        }

        if let memory = memoryUsage, memory > 85 {
            score -= (memory - 85) * 3
        }

        if let disk = diskUsage, disk > 90 {
            score -= (disk - 90) * 5
        }

        if let temp = cpuTemperature, temp > 80 {
            score -= (temp - 80) * 2
        }

        return max(0, score)
    }

    /// 健康状态描述
    var healthStatus: String {
        let score = healthScore
        switch score {
        case 90...100:
            return "优秀"
        case 70..<90:
            return "良好"
        case 50..<70:
            return "一般"
        case 30..<50:
            return "较差"
        default:
            return "危险"
        }
    }
}
