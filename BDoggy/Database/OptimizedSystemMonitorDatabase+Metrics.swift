//
//  OptimizedSystemMonitorDatabase+Metrics.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  性能指标数据库操作扩展
//

import Foundation
import SQLite3

// MARK: - CPU指标操作

/**
 * CPU性能指标数据库操作扩展
 * 
 * 提供CPU性能数据的存储、查询和分析功能，包括：
 * - CPU使用率（总体、用户态、系统态、空闲）
 * - 系统负载平均值（1分钟、5分钟、15分钟）
 * - CPU核心数和频率信息
 * - 时间序列数据支持
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 插入CPU指标数据
     * 
     * 将CPU性能指标数据存储到数据库中，支持完整的CPU状态信息。
     * 
     * 数据转换和存储策略：
     * - 百分比值乘以100存储为整数（提高精度，节省空间）
     * - 时间戳转换为Unix时间戳
     * - 可选字段使用NULL值处理
     * - 使用预编译语句提高性能
     * 
     * @param metrics CPU指标数据模型
     * @return 操作结果，成功返回.success(())，失败返回DatabaseError
     */
    func insertCPUMetrics(_ metrics: CPUMetricsModel) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT INTO cpu_metrics
                (timestamp, usage_percent, user_percent, system_percent, idle_percent,
                 load_avg_1min, load_avg_5min, load_avg_15min, core_count, frequency_mhz)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_cpu_metrics") else {
                throw DatabaseError.queryError("准备CPU指标插入语句失败")
            }

            // 绑定时间戳
            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            
            // 绑定百分比参数（转换为0-10000的整数以提高精度）
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.userPercent * 100))
            sqlite3_bind_int(statement, 4, Int32(metrics.systemPercent * 100))
            sqlite3_bind_int(statement, 5, Int32(metrics.idlePercent * 100))

            // 绑定负载平均值（可选字段）
            if let loadAvg1 = metrics.loadAvg1min {
                sqlite3_bind_double(statement, 6, loadAvg1)
            } else {
                sqlite3_bind_null(statement, 6)
            }

            if let loadAvg5 = metrics.loadAvg5min {
                sqlite3_bind_double(statement, 7, loadAvg5)
            } else {
                sqlite3_bind_null(statement, 7)
            }

            if let loadAvg15 = metrics.loadAvg15min {
                sqlite3_bind_double(statement, 8, loadAvg15)
            } else {
                sqlite3_bind_null(statement, 8)
            }

            // 绑定CPU核心数
            sqlite3_bind_int(statement, 9, Int32(metrics.coreCount))

            // 绑定CPU频率（可选字段）
            if let frequency = metrics.frequencyMHz {
                sqlite3_bind_int(statement, 10, Int32(frequency))
            } else {
                sqlite3_bind_null(statement, 10)
            }

            // 执行插入操作
            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入CPU指标失败: \(errorMessage)")
            }
        }
    }

    /**
     * 批量插入CPU指标数据（内部方法，不使用事务）
     * 
     * 高性能的批量插入实现，避免重复的事务开销。
     * 
     * @param metricsList CPU指标数据数组
     * @throws DatabaseError 插入失败时抛出错误
     */
    private func insertCPUMetricsBatchInternal(_ metricsList: [CPUMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO cpu_metrics
            (timestamp, usage_percent, user_percent, system_percent, idle_percent,
             load_avg_1min, load_avg_5min, load_avg_15min, core_count, frequency_mhz)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备CPU指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.userPercent * 100))
            sqlite3_bind_int(statement, 4, Int32(metrics.systemPercent * 100))
            sqlite3_bind_int(statement, 5, Int32(metrics.idlePercent * 100))

            if let loadAvg1 = metrics.loadAvg1min {
                sqlite3_bind_double(statement, 6, loadAvg1)
            } else {
                sqlite3_bind_null(statement, 6)
            }

            if let loadAvg5 = metrics.loadAvg5min {
                sqlite3_bind_double(statement, 7, loadAvg5)
            } else {
                sqlite3_bind_null(statement, 7)
            }

            if let loadAvg15 = metrics.loadAvg15min {
                sqlite3_bind_double(statement, 8, loadAvg15)
            } else {
                sqlite3_bind_null(statement, 8)
            }

            sqlite3_bind_int(statement, 9, Int32(metrics.coreCount))

            if let frequency = metrics.frequencyMHz {
                sqlite3_bind_int(statement, 10, Int32(frequency))
            } else {
                sqlite3_bind_null(statement, 10)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入CPU指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入CPU指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /**
     * 批量插入CPU指标数据（公共接口，使用事务）
     * 
     * 提供事务保护的批量插入接口，确保数据一致性。
     * 
     * @param metricsList CPU指标数据数组
     * @return 操作结果，成功返回.success(())，失败返回DatabaseError
     */
    func insertCPUMetricsBatch(_ metricsList: [CPUMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertCPUMetricsBatchInternal(metricsList)
        }
    }

    /**
     * 获取CPU指标数据
     * 
     * 查询指定时间范围内的CPU性能指标数据。
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param limit 最大返回记录数（默认1000）
     * @return 操作结果，成功返回CPUMetricsModel数组，失败返回DatabaseError
     */
    func getCPUMetrics(from startDate: Date, to endDate: Date, limit: Int = 1000) -> Result<[CPUMetricsModel], DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT timestamp, usage_percent, user_percent, system_percent, idle_percent,
                       load_avg_1min, load_avg_5min, load_avg_15min, core_count, frequency_mhz
                FROM cpu_metrics
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备CPU指标查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))
            sqlite3_bind_int(statement, 3, Int32(limit))

            var metrics: [CPUMetricsModel] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))
                let usagePercent = Double(sqlite3_column_int(statement, 1)) / 100.0
                let userPercent = Double(sqlite3_column_int(statement, 2)) / 100.0
                let systemPercent = Double(sqlite3_column_int(statement, 3)) / 100.0
                let idlePercent = Double(sqlite3_column_int(statement, 4)) / 100.0

                let loadAvg1min: Double? = {
                    if sqlite3_column_type(statement, 5) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 5)
                    }
                    return nil
                }()

                let loadAvg5min: Double? = {
                    if sqlite3_column_type(statement, 6) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 6)
                    }
                    return nil
                }()

                let loadAvg15min: Double? = {
                    if sqlite3_column_type(statement, 7) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 7)
                    }
                    return nil
                }()

                let coreCount = Int(sqlite3_column_int(statement, 8))

                let frequencyMHz: Int? = {
                    if sqlite3_column_type(statement, 9) != SQLITE_NULL {
                        return Int(sqlite3_column_int(statement, 9))
                    }
                    return nil
                }()

                let cpuMetrics = CPUMetricsModel(
                    timestamp: timestamp,
                    usagePercent: usagePercent,
                    userPercent: userPercent,
                    systemPercent: systemPercent,
                    idlePercent: idlePercent,
                    loadAvg1min: loadAvg1min,
                    loadAvg5min: loadAvg5min,
                    loadAvg15min: loadAvg15min,
                    coreCount: coreCount,
                    frequencyMHz: frequencyMHz
                )

                metrics.append(cpuMetrics)
            }

            return .success(metrics)
        }
    }
}

// MARK: - 内存指标操作

/**
 * 内存性能指标数据库操作扩展
 *
 * 提供内存使用数据的存储、查询和分析功能，包括：
 * - 内存使用率和各类内存分配
 * - 活跃、非活跃、有线、压缩内存
 * - 交换文件使用情况
 * - 内存压力指标
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 插入内存指标数据
     *
     * 将内存性能指标数据存储到数据库中，包含完整的内存状态信息。
     *
     * @param metrics 内存指标数据模型
     * @return 操作结果，成功返回.success(())，失败返回DatabaseError
     */
    func insertMemoryMetrics(_ metrics: MemoryMetricsModel) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT INTO memory_metrics
                (timestamp, usage_percent, used_mb, available_mb, total_mb, active_mb,
                 inactive_mb, wired_mb, compressed_mb, swap_used_mb, swap_total_mb, memory_pressure)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_memory_metrics") else {
                throw DatabaseError.queryError("准备内存指标插入语句失败")
            }

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.usedMB))
            sqlite3_bind_int(statement, 4, Int32(metrics.availableMB))
            sqlite3_bind_int(statement, 5, Int32(metrics.totalMB))
            sqlite3_bind_int(statement, 6, Int32(metrics.activeMB))
            sqlite3_bind_int(statement, 7, Int32(metrics.inactiveMB))
            sqlite3_bind_int(statement, 8, Int32(metrics.wiredMB))
            sqlite3_bind_int(statement, 9, Int32(metrics.compressedMB))
            sqlite3_bind_int(statement, 10, Int32(metrics.swapUsedMB))
            sqlite3_bind_int(statement, 11, Int32(metrics.swapTotalMB))
            sqlite3_bind_int(statement, 12, Int32(metrics.memoryPressure))

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入内存指标失败: \(errorMessage)")
            }
        }
    }

    /**
     * 批量插入内存指标数据（内部方法，不使用事务）
     *
     * 高性能的批量插入实现，避免重复的事务开销。
     *
     * @param metricsList 内存指标数据数组
     * @throws DatabaseError 插入失败时抛出错误
     */
    private func insertMemoryMetricsBatchInternal(_ metricsList: [MemoryMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO memory_metrics
            (timestamp, usage_percent, used_mb, available_mb, total_mb, active_mb,
             inactive_mb, wired_mb, compressed_mb, swap_used_mb, swap_total_mb, memory_pressure)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备内存指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.usedMB))
            sqlite3_bind_int(statement, 4, Int32(metrics.availableMB))
            sqlite3_bind_int(statement, 5, Int32(metrics.totalMB))
            sqlite3_bind_int(statement, 6, Int32(metrics.activeMB))
            sqlite3_bind_int(statement, 7, Int32(metrics.inactiveMB))
            sqlite3_bind_int(statement, 8, Int32(metrics.wiredMB))
            sqlite3_bind_int(statement, 9, Int32(metrics.compressedMB))
            sqlite3_bind_int(statement, 10, Int32(metrics.swapUsedMB))
            sqlite3_bind_int(statement, 11, Int32(metrics.swapTotalMB))
            sqlite3_bind_int(statement, 12, Int32(metrics.memoryPressure))

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入内存指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入内存指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /**
     * 批量插入内存指标数据（公共接口，使用事务）
     *
     * 提供事务保护的批量插入接口，确保数据一致性。
     *
     * @param metricsList 内存指标数据数组
     * @return 操作结果，成功返回.success(())，失败返回DatabaseError
     */
    func insertMemoryMetricsBatch(_ metricsList: [MemoryMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertMemoryMetricsBatchInternal(metricsList)
        }
    }
}
