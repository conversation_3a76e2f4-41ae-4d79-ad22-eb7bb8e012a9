//
//  DatabaseMigrationManager.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库迁移管理器
//

import Foundation
import os.log
import SQLite3

/// 数据库迁移管理器
/// 负责管理数据库版本升级和数据迁移
class DatabaseMigrationManager {
    private let database: OptimizedSystemMonitorDatabase
    private let logger = Logger.self
    
    // 当前数据库版本
    static let currentVersion = 2
    
    init(database: OptimizedSystemMonitorDatabase) {
        self.database = database
    }
    
    // MARK: - 迁移管理
    
    /// 执行数据库迁移
    func performMigration() -> Result<Void, DatabaseMigrationError> {
        let currentVersion = database.getCurrentVersion()
        let targetVersion = Self.currentVersion
        
        guard currentVersion < targetVersion else {
            logger.info("数据库版本已是最新: \(currentVersion)")
            return .success(())
        }
        
        logger.info("开始数据库迁移: \(currentVersion) -> \(targetVersion)")
        
        // 创建备份
        switch createBackup() {
        case .success(let backupPath):
            logger.info("数据库备份创建成功: \(backupPath)")
        case .failure(let error):
            logger.error("创建数据库备份失败: \(error.localizedDescription)")
            return .failure(error)
        }
        
        // 执行迁移步骤
        for version in (currentVersion + 1) ... targetVersion {
            switch executeMigrationStep(toVersion: version) {
            case .success():
                logger.info("迁移到版本 \(version) 成功")
            case .failure(let error):
                logger.error("迁移到版本 \(version) 失败: \(error.localizedDescription)")
                // 尝试恢复备份
                _ = restoreFromBackup()
                return .failure(error)
            }
        }
        
        logger.info("数据库迁移完成")
        return .success(())
    }
    
    /// 执行单个迁移步骤
    private func executeMigrationStep(toVersion version: Int) -> Result<Void, DatabaseMigrationError> {
        switch version {
        case 1:
            return migrateToVersion1()
        case 2:
            return migrateToVersion2()
        default:
            return .failure(.unsupportedVersion(version))
        }
    }
    
    // MARK: - 具体迁移步骤
    
    /// 迁移到版本1（初始版本）
    private func migrateToVersion1() -> Result<Void, DatabaseMigrationError> {
        return database.executeInTransaction {
            // 版本1是初始版本，只需要记录版本号
            let sql = "INSERT INTO schema_versions (version_number, description) VALUES (1, '初始数据库版本');"
            
            if !database.executeSQL(sql, description: "记录版本1") {
                throw DatabaseMigrationError.migrationFailed("记录版本1失败")
            }
        }.mapError { error in
            if let migrationError = error as? DatabaseMigrationError {
                return migrationError
            }
            return .migrationFailed(error.localizedDescription)
        }
    }
    
    /// 迁移到版本2（添加新字段和索引）
    private func migrateToVersion2() -> Result<Void, DatabaseMigrationError> {
        return database.executeInTransaction {
            // TODO: 更新到版本2 在此写更新逻辑
            // 迁移现有数据（如果需要）
            try migrateExistingDataToVersion2()
            
            // 记录版本 当升级时放开
//            let versionSQL = "INSERT INTO schema_versions (version_number, description) VALUES (2, '添加新字段和索引，优化数据结构');"
//            if !database.executeSQL(versionSQL, description: "记录版本2") {
//                throw DatabaseMigrationError.migrationFailed("记录版本2失败")
//            }
        }.mapError { error in
            if let migrationError = error as? DatabaseMigrationError {
                return migrationError
            }
            return .migrationFailed(error.localizedDescription)
        }
    }
    
    /// 迁移现有数据到版本2
    private func migrateExistingDataToVersion2() throws {
        // TODO: 更新系统信息表的新字段 在此写更新逻辑
        logger.info("现有数据迁移到版本2完成")
    }
    
    // MARK: - 备份和恢复
    
    /// 创建数据库备份
    private func createBackup() -> Result<String, DatabaseMigrationError> {
        let databasePath = database.getDatabasePath()
        let backupPath = databasePath + ".backup.\(Int(Date().timeIntervalSince1970))"
        
        do {
            try FileManager.default.copyItem(atPath: databasePath, toPath: backupPath)
            return .success(backupPath)
        } catch {
            return .failure(.backupFailed(error.localizedDescription))
        }
    }
    
    /// 从备份恢复数据库
    private func restoreFromBackup() -> Result<Void, DatabaseMigrationError> {
        let databasePath = database.getDatabasePath()
        let backupDirectory = URL(fileURLWithPath: databasePath).deletingLastPathComponent()
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: backupDirectory,
                                                                    includingPropertiesForKeys: nil)
            
            // 找到最新的备份文件
            let backupFiles = files.filter { $0.lastPathComponent.contains(".backup.") }
                .sorted { $0.lastPathComponent > $1.lastPathComponent }
            
            guard let latestBackup = backupFiles.first else {
                return .failure(.noBackupFound)
            }
            
            // 删除当前数据库文件
            try FileManager.default.removeItem(atPath: databasePath)
            
            // 恢复备份
            try FileManager.default.copyItem(at: latestBackup,
                                             to: URL(fileURLWithPath: databasePath))
            
            logger.info("数据库已从备份恢复: \(latestBackup.lastPathComponent)")
            return .success(())
            
        } catch {
            return .failure(.restoreFailed(error.localizedDescription))
        }
    }
    
    /// 清理旧备份文件
    func cleanupOldBackups(keepCount: Int = 5) -> Result<Int, DatabaseMigrationError> {
        let databasePath = database.getDatabasePath()
        let backupDirectory = URL(fileURLWithPath: databasePath).deletingLastPathComponent()
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: backupDirectory,
                                                                    includingPropertiesForKeys: [.creationDateKey])
            
            let backupFiles = files.filter { $0.lastPathComponent.contains(".backup.") }
                .sorted {
                    let date1 = try? $0.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                    let date2 = try? $1.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                    return date1! > date2!
                }
            
            let filesToDelete = Array(backupFiles.dropFirst(keepCount))
            
            for file in filesToDelete {
                try FileManager.default.removeItem(at: file)
            }
            
            logger.info("清理了\(filesToDelete.count)个旧备份文件")
            return .success(filesToDelete.count)
            
        } catch {
            return .failure(.cleanupFailed(error.localizedDescription))
        }
    }
}
