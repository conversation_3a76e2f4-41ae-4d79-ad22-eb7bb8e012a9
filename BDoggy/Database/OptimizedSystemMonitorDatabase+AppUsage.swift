//
//  OptimizedSystemMonitorDatabase+AppUsage.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  应用使用会话管理数据库操作扩展
//

import Foundation
import SQLite3

// MARK: - 应用使用会话操作

/**
 * 应用使用会话管理数据库操作扩展
 * 
 * 提供应用使用会话的完整生命周期管理，包括：
 * - 会话的创建、更新和结束
 * - 活跃会话的查询和管理
 * - 应用使用统计和分析
 * - 会话数据的聚合和报告
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 插入或更新应用使用会话
     * 
     * 根据会话是否已有ID来决定执行插入或更新操作。
     * 支持会话的完整生命周期管理，包括性能指标的记录。
     * 
     * @param session 应用使用会话对象
     * @return 操作结果，成功返回会话ID，失败返回DatabaseError
     */
    func insertOrUpdateAppUsageSession(_ session: AppUsageSession) -> Result<Int64, DatabaseError> {
        return executeInTransaction {
            if let sessionID = session.id {
                // 更新现有会话
                let sql = """
                    UPDATE app_usage_sessions
                    SET session_end = ?, duration_seconds = ?, peak_cpu_usage = ?, avg_cpu_usage = ?,
                        peak_memory_mb = ?, avg_memory_mb = ?, switch_count = ?, is_active = ?, updated_at = ?
                    WHERE id = ?;
                """

                guard let statement = prepareStatement(sql, cacheKey: "update_app_usage_session") else {
                    throw DatabaseError.queryError("准备应用使用会话更新语句失败")
                }

                // 绑定会话结束时间（可选）
                if let sessionEnd = session.sessionEnd {
                    sqlite3_bind_int64(statement, 1, Int64(sessionEnd.timeIntervalSince1970))
                } else {
                    sqlite3_bind_null(statement, 1)
                }

                // 绑定会话统计数据
                sqlite3_bind_int(statement, 2, Int32(session.durationSeconds))
                sqlite3_bind_int(statement, 3, Int32(session.peakCPUUsage * 100))
                sqlite3_bind_int(statement, 4, Int32(session.avgCPUUsage * 100))
                sqlite3_bind_int(statement, 5, Int32(session.peakMemoryMB))
                sqlite3_bind_int(statement, 6, Int32(session.avgMemoryMB))
                sqlite3_bind_int(statement, 7, Int32(session.switchCount))
                sqlite3_bind_int(statement, 8, session.isActive ? 1 : 0)
                sqlite3_bind_int64(statement, 9, Int64(session.updatedAt.timeIntervalSince1970))
                sqlite3_bind_int64(statement, 10, sessionID)

                if sqlite3_step(statement) != SQLITE_DONE {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("更新应用使用会话失败: \(errorMessage)")
                }

                return sessionID
            } else {
                // 插入新会话
                let sql = """
                    INSERT INTO app_usage_sessions
                    (app_id, session_start, session_end, duration_seconds, peak_cpu_usage, avg_cpu_usage,
                     peak_memory_mb, avg_memory_mb, switch_count, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
                """

                guard let statement = prepareStatement(sql, cacheKey: "insert_app_usage_session") else {
                    throw DatabaseError.queryError("准备应用使用会话插入语句失败")
                }

                // 绑定会话基本信息
                sqlite3_bind_int64(statement, 1, session.appID)
                sqlite3_bind_int64(statement, 2, Int64(session.sessionStart.timeIntervalSince1970))

                // 绑定会话结束时间（可选）
                if let sessionEnd = session.sessionEnd {
                    sqlite3_bind_int64(statement, 3, Int64(sessionEnd.timeIntervalSince1970))
                } else {
                    sqlite3_bind_null(statement, 3)
                }

                // 绑定性能统计数据
                sqlite3_bind_int(statement, 4, Int32(session.durationSeconds))
                sqlite3_bind_int(statement, 5, Int32(session.peakCPUUsage * 100))
                sqlite3_bind_int(statement, 6, Int32(session.avgCPUUsage * 100))
                sqlite3_bind_int(statement, 7, Int32(session.peakMemoryMB))
                sqlite3_bind_int(statement, 8, Int32(session.avgMemoryMB))
                sqlite3_bind_int(statement, 9, Int32(session.switchCount))
                sqlite3_bind_int(statement, 10, session.isActive ? 1 : 0)

                if sqlite3_step(statement) != SQLITE_DONE {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("插入应用使用会话失败: \(errorMessage)")
                }

                return sqlite3_last_insert_rowid(db)
            }
        }
    }

    /**
     * 获取活跃的应用使用会话
     * 
     * 查询指定应用当前活跃的使用会话。
     * 通常用于更新正在进行的会话数据。
     * 
     * @param appID 应用ID
     * @return 操作结果，成功返回AppUsageSession对象（可能为nil），失败返回DatabaseError
     */
    func getActiveAppUsageSession(appID: Int64) -> Result<AppUsageSession?, DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT id, app_id, session_start, session_end, duration_seconds, peak_cpu_usage,
                       avg_cpu_usage, peak_memory_mb, avg_memory_mb, switch_count, is_active,
                       created_at, updated_at
                FROM app_usage_sessions
                WHERE app_id = ? AND is_active = 1
                ORDER BY session_start DESC
                LIMIT 1;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备活跃应用会话查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, appID)

            if sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let appID = sqlite3_column_int64(statement, 1)
                let sessionStart = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 2)))

                // 处理可选的会话结束时间
                let sessionEnd: Date? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 3)))
                    }
                    return nil
                }()

                // 提取性能统计数据
                let durationSeconds = Int(sqlite3_column_int(statement, 4))
                let peakCPUUsage = Double(sqlite3_column_int(statement, 5)) / 100.0
                let avgCPUUsage = Double(sqlite3_column_int(statement, 6)) / 100.0
                let peakMemoryMB = Int(sqlite3_column_int(statement, 7))
                let avgMemoryMB = Int(sqlite3_column_int(statement, 8))
                let switchCount = Int(sqlite3_column_int(statement, 9))
                let isActive = sqlite3_column_int(statement, 10) == 1
                let createdAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 11)))
                let updatedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 12)))

                // 构造AppUsageSession对象
                let session = AppUsageSession(
                    id: id,
                    appID: appID,
                    sessionStart: sessionStart,
                    sessionEnd: sessionEnd,
                    durationSeconds: durationSeconds,
                    peakCPUUsage: peakCPUUsage,
                    avgCPUUsage: avgCPUUsage,
                    peakMemoryMB: peakMemoryMB,
                    avgMemoryMB: avgMemoryMB,
                    switchCount: switchCount,
                    isActive: isActive,
                    createdAt: createdAt,
                    updatedAt: updatedAt
                )

                return .success(session)
            }

            return .success(nil)
        }
    }

    /**
     * 结束应用使用会话
     * 
     * 将指定的活跃会话标记为结束，更新结束时间和最终统计数据。
     * 
     * @param sessionID 会话ID
     * @param endTime 会话结束时间（默认为当前时间）
     * @return 操作结果，成功返回true，失败返回DatabaseError
     */
    func endAppUsageSession(sessionID: Int64, endTime: Date = Date()) -> Result<Bool, DatabaseError> {
        return executeInTransaction {
            let sql = """
                UPDATE app_usage_sessions
                SET session_end = ?, is_active = 0, updated_at = ?
                WHERE id = ? AND is_active = 1;
            """

            guard let statement = prepareStatement(sql) else {
                throw DatabaseError.queryError("准备结束应用会话语句失败")
            }

            sqlite3_bind_int64(statement, 1, Int64(endTime.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(Date().timeIntervalSince1970))
            sqlite3_bind_int64(statement, 3, sessionID)

            if sqlite3_step(statement) == SQLITE_DONE {
                let changedRows = sqlite3_changes(db)
                Logger.info("结束应用使用会话: \(sessionID), 影响行数: \(changedRows)", category: "Database")
                return changedRows > 0
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("结束应用会话失败: \(errorMessage)")
            }
        }
    }

    /**
     * 结束所有活跃会话
     * 
     * 将所有当前活跃的会话标记为结束。
     * 通常在应用关闭或系统重启时调用。
     * 
     * @param endTime 统一的结束时间（默认为当前时间）
     * @return 操作结果，成功返回结束的会话数量，失败返回DatabaseError
     */
    func endAllActiveSessions(endTime: Date = Date()) -> Result<Int, DatabaseError> {
        return executeInTransaction {
            let sql = """
                UPDATE app_usage_sessions
                SET session_end = ?, is_active = 0, updated_at = ?
                WHERE is_active = 1;
            """

            guard let statement = prepareStatement(sql) else {
                throw DatabaseError.queryError("准备结束所有活跃会话语句失败")
            }

            sqlite3_bind_int64(statement, 1, Int64(endTime.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(Date().timeIntervalSince1970))

            if sqlite3_step(statement) == SQLITE_DONE {
                let changedRows = sqlite3_changes(db)
                Logger.info("结束所有活跃会话，共结束\(changedRows)个会话", category: "Database")
                return Int(changedRows)
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("结束所有活跃会话失败: \(errorMessage)")
            }
        }
    }

    /**
     * 获取应用使用统计
     *
     * 生成指定时间范围内的应用使用统计报告，
     * 包括使用时长、频率、性能指标等综合数据。
     *
     * @param days 统计天数（默认7天）
     * @param includeSystemApps 是否包含系统应用（默认false）
     * @return 操作结果，成功返回AppUsageStats数组，失败返回DatabaseError
     */
    func getAppUsageStats(days: Int = 7, includeSystemApps: Bool = false) -> Result<[AppUsageStats], DatabaseError> {
        return safeDBSync {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))

            var sql = """
                SELECT ai.bundle_id, ai.app_name, ai.app_category,
                       COUNT(aus.id) as session_count,
                       SUM(aus.duration_seconds) as total_duration,
                       AVG(aus.duration_seconds) as avg_duration,
                       MAX(aus.peak_cpu_usage) as max_cpu,
                       AVG(aus.avg_cpu_usage) as avg_cpu,
                       MAX(aus.peak_memory_mb) as max_memory,
                       AVG(aus.avg_memory_mb) as avg_memory,
                       MIN(aus.session_start) as first_used,
                       MAX(aus.session_start) as last_used
                FROM app_info ai
                LEFT JOIN app_usage_sessions aus ON ai.id = aus.app_id
                WHERE aus.session_start >= ?
            """

            if !includeSystemApps {
                sql += " AND ai.is_system_app = 0"
            }

            sql += """
                GROUP BY ai.id, ai.bundle_id, ai.app_name, ai.app_category
                HAVING COUNT(aus.id) > 0
                ORDER BY total_duration DESC;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用统计查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(cutoffDate.timeIntervalSince1970))

            var stats: [AppUsageStats] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let bundleID = String(cString: sqlite3_column_text(statement, 0))
                let appName = String(cString: sqlite3_column_text(statement, 1))

                let appCategory: String? = {
                    if sqlite3_column_type(statement, 2) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 2))
                    }
                    return nil
                }()

                let sessionCount = Int(sqlite3_column_int(statement, 3))
                let totalDuration = Int(sqlite3_column_int(statement, 4))
                let avgDuration = sqlite3_column_double(statement, 5)
                let maxCPU = Double(sqlite3_column_int(statement, 6)) / 100.0
                let avgCPU = Double(sqlite3_column_int(statement, 7)) / 100.0
                let maxMemory = Int(sqlite3_column_int(statement, 8))
                let avgMemory = sqlite3_column_double(statement, 9)
                let firstUsed = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 10)))
                let lastUsed = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 11)))

                let stat = AppUsageStats(
                    bundleID: bundleID,
                    appName: appName,
                    appCategory: appCategory,
                    sessionCount: sessionCount,
                    totalDurationSeconds: totalDuration,
                    avgDurationSeconds: avgDuration,
                    maxCPUUsage: maxCPU,
                    avgCPUUsage: avgCPU,
                    maxMemoryMB: maxMemory,
                    avgMemoryMB: avgMemory,
                    firstUsed: firstUsed,
                    lastUsed: lastUsed
                )

                stats.append(stat)
            }

            return .success(stats)
        }
    }

    /**
     * 获取应用使用会话列表
     *
     * 查询指定应用在指定时间范围内的所有使用会话。
     *
     * @param appID 应用ID（可选，不指定则查询所有应用）
     * @param from 开始时间
     * @param to 结束时间
     * @param limit 最大返回数量（默认100）
     * @return 操作结果，成功返回AppUsageSession数组，失败返回DatabaseError
     */
    func getAppUsageSessions(appID: Int64? = nil, from startDate: Date, to endDate: Date, limit: Int = 100) -> Result<[AppUsageSession], DatabaseError> {
        return safeDBSync {
            var sql = """
                SELECT id, app_id, session_start, session_end, duration_seconds, peak_cpu_usage,
                       avg_cpu_usage, peak_memory_mb, avg_memory_mb, switch_count, is_active,
                       created_at, updated_at
                FROM app_usage_sessions
                WHERE session_start BETWEEN ? AND ?
            """

            if let appID = appID {
                sql += " AND app_id = ?"
            }

            sql += " ORDER BY session_start DESC LIMIT ?;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用会话查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var paramIndex: Int32 = 1
            sqlite3_bind_int64(statement, paramIndex, Int64(startDate.timeIntervalSince1970))
            paramIndex += 1
            sqlite3_bind_int64(statement, paramIndex, Int64(endDate.timeIntervalSince1970))
            paramIndex += 1

            if let appID = appID {
                sqlite3_bind_int64(statement, paramIndex, appID)
                paramIndex += 1
            }

            sqlite3_bind_int(statement, paramIndex, Int32(limit))

            var sessions: [AppUsageSession] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let appID = sqlite3_column_int64(statement, 1)
                let sessionStart = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 2)))

                let sessionEnd: Date? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 3)))
                    }
                    return nil
                }()

                let durationSeconds = Int(sqlite3_column_int(statement, 4))
                let peakCPUUsage = Double(sqlite3_column_int(statement, 5)) / 100.0
                let avgCPUUsage = Double(sqlite3_column_int(statement, 6)) / 100.0
                let peakMemoryMB = Int(sqlite3_column_int(statement, 7))
                let avgMemoryMB = Int(sqlite3_column_int(statement, 8))
                let switchCount = Int(sqlite3_column_int(statement, 9))
                let isActive = sqlite3_column_int(statement, 10) == 1
                let createdAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 11)))
                let updatedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 12)))

                let session = AppUsageSession(
                    id: id,
                    appID: appID,
                    sessionStart: sessionStart,
                    sessionEnd: sessionEnd,
                    durationSeconds: durationSeconds,
                    peakCPUUsage: peakCPUUsage,
                    avgCPUUsage: avgCPUUsage,
                    peakMemoryMB: peakMemoryMB,
                    avgMemoryMB: avgMemoryMB,
                    switchCount: switchCount,
                    isActive: isActive,
                    createdAt: createdAt,
                    updatedAt: updatedAt
                )

                sessions.append(session)
            }

            return .success(sessions)
        }
    }

    /**
     * 删除应用使用会话
     *
     * 删除指定的应用使用会话记录。
     *
     * @param sessionID 会话ID
     * @return 操作结果，成功返回true，失败返回DatabaseError
     */
    func deleteAppUsageSession(sessionID: Int64) -> Result<Bool, DatabaseError> {
        return executeInTransaction {
            let sql = "DELETE FROM app_usage_sessions WHERE id = ?;"

            guard let statement = prepareStatement(sql) else {
                throw DatabaseError.queryError("准备删除应用使用会话语句失败")
            }

            sqlite3_bind_int64(statement, 1, sessionID)

            if sqlite3_step(statement) == SQLITE_DONE {
                let deletedCount = sqlite3_changes(db)
                Logger.info("删除应用使用会话: \(sessionID), 影响行数: \(deletedCount)", category: "Database")
                return deletedCount > 0
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("删除应用使用会话失败: \(errorMessage)")
            }
        }
    }
}
