//
//  OptimizedSystemMonitorDatabase+Analytics.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  高性能查询和数据分析扩展
//

import Foundation
import SQLite3

// MARK: - 高性能查询和数据分析

/**
 * 高性能查询和数据分析扩展
 * 
 * 提供复杂的数据分析和聚合查询功能，包括：
 * - 系统性能概览和趋势分析
 * - 应用使用模式分析
 * - 资源使用峰值检测
 * - 时间序列数据聚合
 * - 多维度数据统计
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 获取系统性能概览（优化版）
     * 
     * 使用时间分组聚合来减少数据量，提高查询性能。
     * 支持自定义采样间隔，平衡数据精度和查询效率。
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param sampleInterval 采样间隔（秒，默认300秒即5分钟）
     * @return 操作结果，成功返回SystemPerformanceOverview数组，失败返回DatabaseError
     */
    func getSystemPerformanceOverview(from startDate: Date, to endDate: Date,
                                      sampleInterval: TimeInterval = 300) -> Result<[SystemPerformanceOverview], DatabaseError>
    {
        return safeDBSync {
            // 使用时间分组来减少数据量，提高查询性能
            let intervalSeconds = Int(sampleInterval)

            let sql = """
                WITH time_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(usage_percent) as avg_cpu_usage
                    FROM cpu_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                memory_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(usage_percent) as avg_memory_usage
                    FROM memory_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                disk_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(usage_percent) as avg_disk_usage
                    FROM disk_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                battery_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(level_percent) as avg_battery_level
                    FROM battery_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                thermal_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(cpu_temperature_celsius) as avg_cpu_temp
                    FROM thermal_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                )
                SELECT 
                    tg.time_group,
                    COALESCE(tg.avg_cpu_usage, 0) / 100.0 as cpu_usage,
                    COALESCE(mg.avg_memory_usage, 0) / 100.0 as memory_usage,
                    COALESCE(dg.avg_disk_usage, 0) / 100.0 as disk_usage,
                    COALESCE(bg.avg_battery_level, 0) / 100.0 as battery_level,
                    COALESCE(thg.avg_cpu_temp, 0) as cpu_temp
                FROM time_groups tg
                LEFT JOIN memory_groups mg ON tg.time_group = mg.time_group
                LEFT JOIN disk_groups dg ON tg.time_group = dg.time_group
                LEFT JOIN battery_groups bg ON tg.time_group = bg.time_group
                LEFT JOIN thermal_groups thg ON tg.time_group = thg.time_group
                ORDER BY tg.time_group;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备系统性能概览查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            let startTimestamp = Int64(startDate.timeIntervalSince1970)
            let endTimestamp = Int64(endDate.timeIntervalSince1970)

            // 绑定所有时间参数
            sqlite3_bind_int64(statement, 1, startTimestamp)
            sqlite3_bind_int64(statement, 2, endTimestamp)
            sqlite3_bind_int64(statement, 3, startTimestamp)
            sqlite3_bind_int64(statement, 4, endTimestamp)
            sqlite3_bind_int64(statement, 5, startTimestamp)
            sqlite3_bind_int64(statement, 6, endTimestamp)
            sqlite3_bind_int64(statement, 7, startTimestamp)
            sqlite3_bind_int64(statement, 8, endTimestamp)
            sqlite3_bind_int64(statement, 9, startTimestamp)
            sqlite3_bind_int64(statement, 10, endTimestamp)

            var overviews: [SystemPerformanceOverview] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))
                let cpuUsage = sqlite3_column_double(statement, 1)
                let memoryUsage = sqlite3_column_double(statement, 2)
                let diskUsage = sqlite3_column_double(statement, 3)
                let batteryLevel = sqlite3_column_double(statement, 4)
                let cpuTemp = sqlite3_column_double(statement, 5)

                let overview = SystemPerformanceOverview(
                    timestamp: timestamp,
                    cpuUsagePercent: cpuUsage,
                    memoryUsagePercent: memoryUsage,
                    diskUsagePercent: diskUsage > 0 ? diskUsage : nil,
                    batteryLevelPercent: batteryLevel > 0 ? batteryLevel : nil,
                    cpuTemperatureCelsius: cpuTemp > 0 ? cpuTemp : nil,
                    activeAppCount: nil,
                    networkActivityLevel: nil
                )

                overviews.append(overview)
            }

            return .success(overviews)
        }
    }

    /**
     * 获取资源使用峰值
     * 
     * 查找指定时间范围内各类资源的使用峰值。
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 操作结果，成功返回峰值统计字典，失败返回DatabaseError
     */
    func getResourceUsagePeaks(from startDate: Date, to endDate: Date) -> Result<[String: Any], DatabaseError> {
        return safeDBSync {
            let startTimestamp = Int64(startDate.timeIntervalSince1970)
            let endTimestamp = Int64(endDate.timeIntervalSince1970)

            var peaks: [String: Any] = [:]

            // CPU峰值
            let cpuSQL = """
                SELECT MAX(usage_percent) / 100.0 as max_cpu, 
                       timestamp as peak_time
                FROM cpu_metrics 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY usage_percent DESC 
                LIMIT 1;
            """

            if let statement = prepareStatement(cpuSQL) {
                sqlite3_bind_int64(statement, 1, startTimestamp)
                sqlite3_bind_int64(statement, 2, endTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    let maxCPU = sqlite3_column_double(statement, 0)
                    let peakTime = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 1)))
                    peaks["max_cpu_usage"] = maxCPU
                    peaks["cpu_peak_time"] = peakTime
                }

                sqlite3_finalize(statement)
            }

            // 内存峰值
            let memorySQL = """
                SELECT MAX(usage_percent) / 100.0 as max_memory,
                       MAX(used_mb) as max_used_mb,
                       timestamp as peak_time
                FROM memory_metrics 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY usage_percent DESC 
                LIMIT 1;
            """

            if let statement = prepareStatement(memorySQL) {
                sqlite3_bind_int64(statement, 1, startTimestamp)
                sqlite3_bind_int64(statement, 2, endTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    let maxMemory = sqlite3_column_double(statement, 0)
                    let maxUsedMB = sqlite3_column_int(statement, 1)
                    let peakTime = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 2)))
                    peaks["max_memory_usage"] = maxMemory
                    peaks["max_memory_used_mb"] = maxUsedMB
                    peaks["memory_peak_time"] = peakTime
                }

                sqlite3_finalize(statement)
            }

            // 温度峰值
            let thermalSQL = """
                SELECT MAX(cpu_temperature_celsius) as max_temp,
                       timestamp as peak_time
                FROM thermal_metrics 
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY cpu_temperature_celsius DESC 
                LIMIT 1;
            """

            if let statement = prepareStatement(thermalSQL) {
                sqlite3_bind_int64(statement, 1, startTimestamp)
                sqlite3_bind_int64(statement, 2, endTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    let maxTemp = sqlite3_column_double(statement, 0)
                    let peakTime = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 1)))
                    peaks["max_cpu_temperature"] = maxTemp
                    peaks["temperature_peak_time"] = peakTime
                }

                sqlite3_finalize(statement)
            }

            return .success(peaks)
        }
    }

    /**
     * 获取应用使用趋势
     * 
     * 分析应用使用模式和趋势，包括使用频率、时长变化等。
     * 
     * @param days 分析天数（默认30天）
     * @param topN 返回前N个应用（默认10个）
     * @return 操作结果，成功返回趋势分析结果，失败返回DatabaseError
     */
    func getAppUsageTrends(days: Int = 30, topN: Int = 10) -> Result<[[String: Any]], DatabaseError> {
        return safeDBSync {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
            let cutoffTimestamp = Int64(cutoffDate.timeIntervalSince1970)

            let sql = """
                SELECT 
                    ai.app_name,
                    ai.bundle_id,
                    COUNT(aus.id) as total_sessions,
                    SUM(aus.duration_seconds) as total_duration,
                    AVG(aus.duration_seconds) as avg_session_duration,
                    COUNT(DISTINCT DATE(aus.session_start, 'unixepoch')) as active_days,
                    MIN(aus.session_start) as first_session,
                    MAX(aus.session_start) as last_session,
                    AVG(aus.avg_cpu_usage) / 100.0 as avg_cpu_usage,
                    AVG(aus.avg_memory_mb) as avg_memory_usage
                FROM app_info ai
                JOIN app_usage_sessions aus ON ai.id = aus.app_id
                WHERE aus.session_start >= ?
                  AND ai.is_system_app = 0
                GROUP BY ai.id, ai.app_name, ai.bundle_id
                HAVING total_sessions > 0
                ORDER BY total_duration DESC
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用趋势查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, cutoffTimestamp)
            sqlite3_bind_int(statement, 2, Int32(topN))

            var trends: [[String: Any]] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let appName = String(cString: sqlite3_column_text(statement, 0))
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let totalSessions = sqlite3_column_int(statement, 2)
                let totalDuration = sqlite3_column_int(statement, 3)
                let avgSessionDuration = sqlite3_column_double(statement, 4)
                let activeDays = sqlite3_column_int(statement, 5)
                let firstSession = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 6)))
                let lastSession = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 7)))
                let avgCPUUsage = sqlite3_column_double(statement, 8)
                let avgMemoryUsage = sqlite3_column_double(statement, 9)

                let trend: [String: Any] = [
                    "app_name": appName,
                    "bundle_id": bundleID,
                    "total_sessions": totalSessions,
                    "total_duration_seconds": totalDuration,
                    "avg_session_duration_seconds": avgSessionDuration,
                    "active_days": activeDays,
                    "first_session": firstSession,
                    "last_session": lastSession,
                    "avg_cpu_usage": avgCPUUsage,
                    "avg_memory_usage_mb": avgMemoryUsage,
                    "usage_frequency": Double(totalSessions) / Double(days), // 每天平均会话数
                    "daily_usage_hours": Double(totalDuration) / 3600.0 / Double(days) // 每天平均使用小时数
                ]

                trends.append(trend)
            }

            return .success(trends)
        }
    }

    /**
     * 获取时间序列数据
     * 
     * 获取指定指标的时间序列数据，支持多种聚合方式。
     * 
     * @param metric 指标类型（cpu、memory、disk等）
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param interval 时间间隔（秒）
     * @param aggregation 聚合方式（avg、max、min）
     * @return 操作结果，成功返回时间序列数据，失败返回DatabaseError
     */
    func getTimeSeriesData(metric: String, from startDate: Date, to endDate: Date, 
                          interval: TimeInterval = 3600, aggregation: String = "avg") -> Result<[[String: Any]], DatabaseError> {
        return safeDBSync {
            let intervalSeconds = Int(interval)
            let startTimestamp = Int64(startDate.timeIntervalSince1970)
            let endTimestamp = Int64(endDate.timeIntervalSince1970)

            let tableName: String
            let valueColumn: String

            switch metric.lowercased() {
            case "cpu":
                tableName = "cpu_metrics"
                valueColumn = "usage_percent"
            case "memory":
                tableName = "memory_metrics"
                valueColumn = "usage_percent"
            case "disk":
                tableName = "disk_metrics"
                valueColumn = "usage_percent"
            case "battery":
                tableName = "battery_metrics"
                valueColumn = "level_percent"
            case "temperature":
                tableName = "thermal_metrics"
                valueColumn = "cpu_temperature_celsius"
            default:
                return .failure(.dataValidationError("不支持的指标类型: \(metric)"))
            }

            let aggregationFunc = aggregation.uppercased()
            guard ["AVG", "MAX", "MIN", "SUM"].contains(aggregationFunc) else {
                return .failure(.dataValidationError("不支持的聚合方式: \(aggregation)"))
            }

            let sql = """
                SELECT 
                    (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                    \(aggregationFunc)(\(valueColumn)) as value
                FROM \(tableName)
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY time_group
                ORDER BY time_group;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备时间序列查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, startTimestamp)
            sqlite3_bind_int64(statement, 2, endTimestamp)

            var series: [[String: Any]] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = sqlite3_column_int64(statement, 0)
                let value = sqlite3_column_double(statement, 1)

                let dataPoint: [String: Any] = [
                    "timestamp": timestamp,
                    "date": Date(timeIntervalSince1970: TimeInterval(timestamp)),
                    "value": metric == "temperature" ? value : value / 100.0, // 温度不需要除以100
                    "metric": metric,
                    "aggregation": aggregation
                ]

                series.append(dataPoint)
            }

            return .success(series)
        }
    }
}
