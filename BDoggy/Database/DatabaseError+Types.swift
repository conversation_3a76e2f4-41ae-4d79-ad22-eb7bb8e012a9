//
//  DatabaseError+Types.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库错误处理和类型定义
//

import Foundation

// MARK: - 数据库配置常量

/**
 * 数据库配置参数
 * 
 * 集中管理数据库相关的配置常量，便于维护和调整。
 */
enum DatabaseConfig {
    /// 当前数据库版本号
    static let currentVersion = 1
    /// 操作失败时的最大重试次数
    static let maxRetryAttempts = 3
    /// 查询超时时间（秒）
    static let queryTimeoutSeconds = 30
    /// 批量操作的默认批次大小
    static let batchSize = 100
    /// 数据库文件名
    static let databaseFileName = "optimized_system_monitor.db"
    /// 架构文件名
    static let schemaFileName = "OptimizedDatabaseSchema"
}

// MARK: - 数据库错误处理

/**
 * 数据库错误类型定义
 * 
 * 定义了完整的数据库操作错误类型，每种错误都包含：
 * - 详细的错误描述
 * - 错误严重程度
 * - 是否可重试的判断
 * 
 * 这种设计有助于：
 * - 精确的错误诊断
 * - 智能的错误恢复策略
 * - 完整的错误日志记录
 */
enum DatabaseError: Error, LocalizedError {
    case connectionError(String)      // 数据库连接相关错误
    case queryError(String)           // SQL查询执行错误
    case transactionError(String)     // 事务处理错误
    case operationError(String)       // 一般操作错误
    case dataValidationError(String)  // 数据验证错误
    case insertError(String)          // 数据插入错误
    case updateError(String)          // 数据更新错误
    case deleteError(String)          // 数据删除错误
    case configurationError(String)   // 数据库配置错误
    case migrationError(String)       // 数据库迁移错误
    case integrityError(String)       // 数据完整性错误

    /**
     * 错误描述信息
     * 提供用户友好的错误描述，便于问题诊断
     */
    var errorDescription: String? {
        switch self {
        case .connectionError(let message):
            return "数据库连接错误: \(message)"
        case .queryError(let message):
            return "查询错误: \(message)"
        case .transactionError(let message):
            return "事务错误: \(message)"
        case .operationError(let message):
            return "操作错误: \(message)"
        case .dataValidationError(let message):
            return "数据验证错误: \(message)"
        case .insertError(let message):
            return "插入错误: \(message)"
        case .updateError(let message):
            return "更新错误: \(message)"
        case .deleteError(let message):
            return "删除错误: \(message)"
        case .configurationError(let message):
            return "配置错误: \(message)"
        case .migrationError(let message):
            return "迁移错误: \(message)"
        case .integrityError(let message):
            return "完整性错误: \(message)"
        }
    }

    /**
     * 错误严重程度评估
     * 
     * 根据错误类型评估严重程度，用于：
     * - 确定日志级别
     * - 决定是否需要立即处理
     * - 制定恢复策略
     */
    var severity: ErrorSeverity {
        switch self {
        case .connectionError, .configurationError, .migrationError:
            return .critical    // 严重错误，可能导致系统无法正常工作
        case .transactionError, .integrityError:
            return .high        // 高级错误，影响数据一致性
        case .insertError, .updateError, .deleteError:
            return .medium      // 中级错误，影响特定操作
        case .queryError, .operationError, .dataValidationError:
            return .low         // 低级错误，通常可以恢复
        }
    }

    /**
     * 判断错误是否可重试
     * 
     * 某些错误（如网络问题、临时锁定）可以通过重试解决，
     * 而其他错误（如配置错误、数据完整性问题）重试无意义
     */
    var isRetryable: Bool {
        switch self {
        case .connectionError, .transactionError, .queryError:
            return true     // 可能是临时问题，可以重试
        case .configurationError, .migrationError, .integrityError:
            return false    // 系统性问题，重试无效
        case .insertError, .updateError, .deleteError, .operationError, .dataValidationError:
            return false    // 数据或逻辑问题，需要修正后再试
        }
    }
}

// MARK: - 错误严重程度

/**
 * 错误严重程度枚举
 * 
 * 定义了四个严重程度级别，用于错误分类和处理策略制定：
 * - low: 轻微错误，不影响主要功能
 * - medium: 中等错误，影响部分功能
 * - high: 严重错误，影响重要功能
 * - critical: 致命错误，可能导致系统无法使用
 */
enum ErrorSeverity: Int, CaseIterable {
    case low = 1        // 低级错误
    case medium = 2     // 中级错误
    case high = 3       // 高级错误
    case critical = 4   // 严重错误

    /**
     * 错误严重程度的中文描述
     */
    var description: String {
        switch self {
        case .low:
            return "低"
        case .medium:
            return "中"
        case .high:
            return "高"
        case .critical:
            return "严重"
        }
    }
    
    /**
     * 错误严重程度的英文描述
     */
    var englishDescription: String {
        switch self {
        case .low:
            return "Low"
        case .medium:
            return "Medium"
        case .high:
            return "High"
        case .critical:
            return "Critical"
        }
    }
    
    /**
     * 是否需要立即处理
     */
    var requiresImmediateAction: Bool {
        return self.rawValue >= ErrorSeverity.high.rawValue
    }
}

// MARK: - 错误处理辅助方法

extension DatabaseError {
    /**
     * 创建连接错误的便利方法
     */
    static func connectionFailed(_ reason: String) -> DatabaseError {
        return .connectionError("连接失败: \(reason)")
    }
    
    /**
     * 创建查询错误的便利方法
     */
    static func queryFailed(_ sql: String, reason: String) -> DatabaseError {
        return .queryError("查询失败 - SQL: \(sql.prefix(100))..., 原因: \(reason)")
    }
    
    /**
     * 创建事务错误的便利方法
     */
    static func transactionFailed(_ operation: String, reason: String) -> DatabaseError {
        return .transactionError("事务操作失败 - 操作: \(operation), 原因: \(reason)")
    }
}
