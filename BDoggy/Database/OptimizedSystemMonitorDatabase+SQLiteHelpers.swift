//
//  OptimizedSystemMonitorDatabase+SQLiteHelpers.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  SQLite数据库操作辅助方法扩展
//

import Foundation
import SQLite3

// MARK: - SQLite辅助方法扩展

/**
 * SQLite数据库操作辅助方法扩展
 * 
 * 提供一系列便利方法来简化SQLite数据的读取和类型转换，
 * 主要解决以下问题：
 * - NULL值的安全处理
 * - 数据类型转换的统一化
 * - 代码重复的减少
 * - 错误处理的标准化
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 从SQLite列获取可选的Double值
     * 
     * 安全地从SQLite结果集中提取Double类型数据，
     * 支持NULL值处理和数值缩放。
     * 
     * @param statement SQLite预编译语句句柄
     * @param columnIndex 列索引（从0开始）
     * @param scale 缩放因子，用于百分比转换等（默认1.0）
     * @return 可选的Double值，如果列为NULL则返回nil
     */
    func getOptionalDoubleFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32, scale: Double = 1.0) -> Double? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return sqlite3_column_double(statement, columnIndex) / scale
    }

    /**
     * 从SQLite列获取可选的Int值
     * 
     * 安全地从SQLite结果集中提取Int类型数据。
     * 
     * @param statement SQLite预编译语句句柄
     * @param columnIndex 列索引（从0开始）
     * @return 可选的Int值，如果列为NULL则返回nil
     */
    func getOptionalIntFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Int? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return Int(sqlite3_column_int(statement, columnIndex))
    }

    /**
     * 从SQLite列获取可选的Int64值
     * 
     * 安全地从SQLite结果集中提取Int64类型数据，
     * 主要用于时间戳和大整数的处理。
     * 
     * @param statement SQLite预编译语句句柄
     * @param columnIndex 列索引（从0开始）
     * @return 可选的Int64值，如果列为NULL则返回nil
     */
    func getOptionalInt64FromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Int64? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return sqlite3_column_int64(statement, columnIndex)
    }

    /**
     * 从SQLite列获取可选的String值
     * 
     * 安全地从SQLite结果集中提取String类型数据，
     * 处理UTF-8编码和NULL值。
     * 
     * @param statement SQLite预编译语句句柄
     * @param columnIndex 列索引（从0开始）
     * @return 可选的String值，如果列为NULL则返回nil
     */
    func getOptionalStringFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> String? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL,
              let cString = sqlite3_column_text(statement, columnIndex) else { return nil }
        return String(cString: cString)
    }

    /**
     * 从SQLite列获取可选的Date值
     * 
     * 从Unix时间戳转换为Date对象，
     * 统一处理时间数据的存储和读取。
     * 
     * @param statement SQLite预编译语句句柄
     * @param columnIndex 列索引（从0开始）
     * @return 可选的Date值，如果列为NULL则返回nil
     */
    func getOptionalDateFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Date? {
        guard let timestamp = getOptionalInt64FromColumn(statement, columnIndex) else { return nil }
        return Date(timeIntervalSince1970: TimeInterval(timestamp))
    }

    /**
     * 从SQLite列获取可选的Bool值
     * 
     * 从整数值转换为布尔值，
     * SQLite中布尔值通常存储为0/1整数。
     * 
     * @param statement SQLite预编译语句句柄
     * @param columnIndex 列索引（从0开始）
     * @return 可选的Bool值，如果列为NULL则返回nil
     */
    func getOptionalBoolFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Bool? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return sqlite3_column_int(statement, columnIndex) != 0
    }
    
    /**
     * 安全绑定字符串参数到SQLite语句
     * 
     * 统一处理字符串参数的绑定，包括NULL值处理。
     * 
     * @param statement SQLite预编译语句句柄
     * @param index 参数索引（从1开始）
     * @param value 要绑定的字符串值（可选）
     */
    func bindStringParameter(_ statement: OpaquePointer, at index: Int32, value: String?) {
        let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
        
        if let stringValue = value {
            sqlite3_bind_text(statement, index, (stringValue as NSString).utf8String, -1, SQLITE_TRANSIENT)
        } else {
            sqlite3_bind_null(statement, index)
        }
    }
    
    /**
     * 安全绑定整数参数到SQLite语句
     * 
     * 统一处理整数参数的绑定，包括NULL值处理。
     * 
     * @param statement SQLite预编译语句句柄
     * @param index 参数索引（从1开始）
     * @param value 要绑定的整数值（可选）
     */
    func bindIntParameter(_ statement: OpaquePointer, at index: Int32, value: Int?) {
        if let intValue = value {
            sqlite3_bind_int(statement, index, Int32(intValue))
        } else {
            sqlite3_bind_null(statement, index)
        }
    }
    
    /**
     * 安全绑定双精度浮点数参数到SQLite语句
     * 
     * 统一处理Double参数的绑定，包括NULL值处理。
     * 
     * @param statement SQLite预编译语句句柄
     * @param index 参数索引（从1开始）
     * @param value 要绑定的Double值（可选）
     */
    func bindDoubleParameter(_ statement: OpaquePointer, at index: Int32, value: Double?) {
        if let doubleValue = value {
            sqlite3_bind_double(statement, index, doubleValue)
        } else {
            sqlite3_bind_null(statement, index)
        }
    }
    
    /**
     * 安全绑定布尔值参数到SQLite语句
     * 
     * 将布尔值转换为整数（0/1）进行绑定。
     * 
     * @param statement SQLite预编译语句句柄
     * @param index 参数索引（从1开始）
     * @param value 要绑定的布尔值（可选）
     */
    func bindBoolParameter(_ statement: OpaquePointer, at index: Int32, value: Bool?) {
        if let boolValue = value {
            sqlite3_bind_int(statement, index, boolValue ? 1 : 0)
        } else {
            sqlite3_bind_null(statement, index)
        }
    }
    
    /**
     * 安全绑定日期参数到SQLite语句
     * 
     * 将Date对象转换为Unix时间戳进行绑定。
     * 
     * @param statement SQLite预编译语句句柄
     * @param index 参数索引（从1开始）
     * @param value 要绑定的Date值（可选）
     */
    func bindDateParameter(_ statement: OpaquePointer, at index: Int32, value: Date?) {
        if let dateValue = value {
            sqlite3_bind_int64(statement, index, Int64(dateValue.timeIntervalSince1970))
        } else {
            sqlite3_bind_null(statement, index)
        }
    }
    
    /**
     * 获取SQLite错误信息
     * 
     * 从数据库连接中获取最后的错误信息。
     * 
     * @return 错误信息字符串
     */
    func getLastErrorMessage() -> String {
        guard let db = db else { return "数据库连接不可用" }
        return String(cString: sqlite3_errmsg(db))
    }
    
    /**
     * 检查SQLite操作结果
     * 
     * 检查SQLite操作的返回码是否表示成功。
     * 
     * @param resultCode SQLite操作的返回码
     * @return true表示操作成功，false表示失败
     */
    func isSuccessResult(_ resultCode: Int32) -> Bool {
        return resultCode == SQLITE_OK || resultCode == SQLITE_DONE || resultCode == SQLITE_ROW
    }
    
    /**
     * 获取表的列数
     * 
     * 获取指定表的列数量。
     * 
     * @param tableName 表名
     * @return 操作结果，成功返回列数，失败返回DatabaseError
     */
    func getTableColumnCount(tableName: String) -> Result<Int, DatabaseError> {
        return safeDBSync {
            let sql = "PRAGMA table_info(\(tableName));"
            
            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备表信息查询语句失败"))
            }
            
            defer { sqlite3_finalize(statement) }
            
            var columnCount = 0
            while sqlite3_step(statement) == SQLITE_ROW {
                columnCount += 1
            }
            
            return .success(columnCount)
        }
    }
    
    /**
     * 检查表是否存在
     * 
     * 检查指定的表是否在数据库中存在。
     * 
     * @param tableName 表名
     * @return 操作结果，成功返回布尔值，失败返回DatabaseError
     */
    func tableExists(tableName: String) -> Result<Bool, DatabaseError> {
        return safeDBSync {
            let sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=? LIMIT 1;"
            
            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备表存在性检查语句失败"))
            }
            
            defer { sqlite3_finalize(statement) }
            
            bindStringParameter(statement, at: 1, value: tableName)
            
            let exists = sqlite3_step(statement) == SQLITE_ROW
            return .success(exists)
        }
    }
}
