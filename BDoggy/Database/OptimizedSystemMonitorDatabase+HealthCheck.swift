//
//  OptimizedSystemMonitorDatabase+HealthCheck.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库健康监控和诊断扩展
//

import Foundation
import SQLite3

// MARK: - 数据库健康监控和诊断

/**
 * 数据库健康监控和诊断扩展
 *
 * 提供全面的数据库健康检查和诊断功能，包括：
 * - 数据库完整性验证
 * - 性能指标监控
 * - 存储空间分析
 * - 错误统计和趋势分析
 * - 自动化健康评分
 */
extension OptimizedSystemMonitorDatabase {
    /**
     * 执行全面的数据库健康检查
     *
     * 这是一个综合性的健康检查方法，评估数据库的各个方面：
     *
     * 检查项目：
     * 1. 数据库完整性验证
     * 2. 连接状态检查
     * 3. 文件大小和存储分析
     * 4. 表统计和数据分布
     * 5. 查询性能评估
     * 6. 磁盘空间检查
     * 7. 错误和警告统计
     *
     * 评分机制：
     * - 基于各项检查结果计算综合健康评分（0-100）
     * - 提供具体的问题描述和改进建议
     * - 支持趋势分析和预警
     *
     * @return 操作结果，成功返回DatabaseHealthReport，失败返回DatabaseError
     */
    func performHealthCheck() -> Result<DatabaseHealthReport, DatabaseError> {
        return Logger.measureTime("数据库健康检查", category: "Database") {
            var report = DatabaseHealthReport()

            // 1. 数据库完整性检查
            switch checkDatabaseIntegrity() {
            case .success(let isHealthy):
                report.integrityCheck = isHealthy
                if !isHealthy {
                    report.issues.append("数据库完整性检查失败")
                }
            case .failure(let error):
                report.issues.append("完整性检查错误: \(error.localizedDescription)")
            }

            // 2. 数据库连接状态检查
            report.connectionStatus = (db != nil)
            if !report.connectionStatus {
                report.issues.append("数据库连接不可用")
            }

            // 3. 数据库文件大小检查
            let dbSize = getDatabaseSize()
            report.databaseSizeMB = Double(dbSize) / (1024 * 1024)

            // 检查文件大小是否过大
            if report.databaseSizeMB > 500 { // 超过500MB
                report.warnings.append("数据库文件较大: \(String(format: "%.1f", report.databaseSizeMB))MB")
            }

            // 4. 磁盘空间检查
            if let availableSpace = getAvailableDiskSpace() {
                report.availableDiskSpaceMB = Double(availableSpace) / (1024 * 1024)
                
                // 检查可用空间是否充足
                if report.availableDiskSpaceMB < 1024 { // 少于1GB
                    report.warnings.append("可用磁盘空间不足: \(String(format: "%.1f", report.availableDiskSpaceMB))MB")
                }
            }

            // 5. 查询性能评估
            switch evaluateQueryPerformance() {
            case .success(let score):
                report.queryPerformanceScore = score
                if score < 70 {
                    report.warnings.append("查询性能较低: \(score)分")
                }
            case .failure:
                report.warnings.append("无法评估查询性能")
            }

            // 6. 表统计检查
            switch getDatabaseStats() {
            case .success(let stats):
                // 检查是否有异常大的表
                for (tableName, count) in stats.tableCounts {
                    if count > 1000000 { // 超过100万条记录
                        report.warnings.append("表\(tableName)记录数过多: \(count)条")
                    }
                }
            case .failure:
                report.warnings.append("无法获取数据库统计信息")
            }

            // 7. 添加改进建议
            generateRecommendations(for: &report)

            return .success(report)
        }
    }

    /**
     * 检查数据库完整性
     *
     * 执行SQLite的完整性检查，验证：
     * - 数据库文件结构完整性
     * - 索引一致性
     * - 页面链接正确性
     *
     * @return 操作结果，成功返回布尔值，失败返回DatabaseError
     */
    func checkDatabaseIntegrity() -> Result<Bool, DatabaseError> {
        return safeDBSync {
            let sql = "PRAGMA integrity_check;"
            
            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备完整性检查语句失败"))
            }
            
            defer { sqlite3_finalize(statement) }
            
            if sqlite3_step(statement) == SQLITE_ROW {
                let result = String(cString: sqlite3_column_text(statement, 0))
                let isHealthy = result.lowercased() == "ok"
                
                if !isHealthy {
                    Logger.error("数据库完整性检查失败: \(result)", category: "Database")
                }
                
                return .success(isHealthy)
            }
            
            return .failure(.queryError("完整性检查无结果"))
        }
    }

    /**
     * 评估查询性能
     *
     * 通过执行一系列测试查询来评估数据库的查询性能。
     *
     * @return 操作结果，成功返回性能评分（0-100），失败返回DatabaseError
     */
    private func evaluateQueryPerformance() -> Result<Int, DatabaseError> {
        return safeDBSync {
            let testQueries = [
                "SELECT COUNT(*) FROM app_info;",
                "SELECT COUNT(*) FROM cpu_metrics WHERE timestamp > ?;",
                "SELECT app_name FROM app_info ORDER BY app_name LIMIT 10;"
            ]
            
            var totalTime: Double = 0
            let cutoffTime = Date().addingTimeInterval(-86400) // 24小时前
            
            for (index, query) in testQueries.enumerated() {
                let startTime = CFAbsoluteTimeGetCurrent()
                
                guard let statement = prepareStatement(query) else {
                    continue
                }
                
                // 为第二个查询绑定参数
                if index == 1 {
                    sqlite3_bind_int64(statement, 1, Int64(cutoffTime.timeIntervalSince1970))
                }
                
                while sqlite3_step(statement) == SQLITE_ROW {
                    // 消费结果
                }
                
                sqlite3_finalize(statement)
                
                let endTime = CFAbsoluteTimeGetCurrent()
                totalTime += (endTime - startTime)
            }
            
            // 根据总执行时间计算评分
            let avgTime = totalTime / Double(testQueries.count)
            let score: Int
            
            switch avgTime {
            case 0..<0.01:
                score = 100
            case 0.01..<0.05:
                score = 90
            case 0.05..<0.1:
                score = 80
            case 0.1..<0.2:
                score = 70
            case 0.2..<0.5:
                score = 60
            case 0.5..<1.0:
                score = 50
            default:
                score = 30
            }
            
            return .success(score)
        }
    }

    /**
     * 获取可用磁盘空间
     *
     * 获取数据库文件所在磁盘的可用空间。
     *
     * @return 可用空间字节数，获取失败返回nil
     */
    private func getAvailableDiskSpace() -> Int64? {
        do {
            let url = URL(fileURLWithPath:  getDatabasePath())
            let values = try url.resourceValues(forKeys: [.volumeAvailableCapacityKey])
            return values.volumeAvailableCapacity
        } catch {
            Logger.error("获取磁盘可用空间失败", category: "Database", error: error)
            return nil
        }
    }

    /**
     * 生成改进建议
     *
     * 基于健康检查结果生成具体的改进建议。
     *
     * @param report 健康检查报告（引用传递，会被修改）
     */
    private func generateRecommendations(for report: inout DatabaseHealthReport) {
        // 基于数据库大小的建议
        if report.databaseSizeMB > 200 {
            report.recommendations.append("考虑清理过期数据以减少数据库大小")
            report.recommendations.append("定期执行VACUUM操作压缩数据库")
        }
        
        // 基于查询性能的建议
        if report.queryPerformanceScore < 80 {
            report.recommendations.append("考虑重建索引以提高查询性能")
            report.recommendations.append("执行ANALYZE更新查询优化器统计信息")
        }
        
        // 基于磁盘空间的建议
        if report.availableDiskSpaceMB < 2048 { // 少于2GB
            report.recommendations.append("清理磁盘空间，确保有足够的存储空间")
        }
        
        // 基于完整性检查的建议
        if !report.integrityCheck {
            report.recommendations.append("数据库可能已损坏，建议备份数据并重建数据库")
        }
        
        // 基于问题数量的建议
        if report.issues.count > 3 {
            report.recommendations.append("存在多个问题，建议进行全面的数据库维护")
        }
        
        // 通用建议
        if report.recommendations.isEmpty {
            report.recommendations.append("数据库状态良好，建议定期进行健康检查")
        }
    }

    /**
     * 生成诊断报告
     *
     * 生成详细的数据库诊断报告，包含所有检查项目的详细信息。
     *
     * @return 操作结果，成功返回诊断报告字符串，失败返回DatabaseError
     */
    func generateDiagnosticReport() -> Result<String, DatabaseError> {
        switch performHealthCheck() {
        case .success(let healthReport):
            var report = "=== 数据库诊断报告 ===\n"
            report += "生成时间: \(DateFormatter.localizedString(from: healthReport.checkTime, dateStyle: .medium, timeStyle: .medium))\n\n"
            
            // 总体健康状态
            report += "总体健康评分: \(healthReport.overallHealthScore)/100 (\(healthReport.healthDescription))\n"
            report += "数据库大小: \(String(format: "%.1f", healthReport.databaseSizeMB))MB\n"
            report += "可用磁盘空间: \(String(format: "%.1f", healthReport.availableDiskSpaceMB))MB\n"
            report += "查询性能评分: \(healthReport.queryPerformanceScore)/100\n\n"
            
            // 检查结果
            report += "=== 检查结果 ===\n"
            report += "完整性检查: \(healthReport.integrityCheck ? "通过" : "失败")\n"
            report += "连接状态: \(healthReport.connectionStatus ? "正常" : "异常")\n\n"
            
            // 问题列表
            if !healthReport.issues.isEmpty {
                report += "=== 发现的问题 ===\n"
                for (index, issue) in healthReport.issues.enumerated() {
                    report += "\(index + 1). \(issue)\n"
                }
                report += "\n"
            }
            
            // 警告列表
            if !healthReport.warnings.isEmpty {
                report += "=== 警告信息 ===\n"
                for (index, warning) in healthReport.warnings.enumerated() {
                    report += "\(index + 1). \(warning)\n"
                }
                report += "\n"
            }
            
            // 改进建议
            if !healthReport.recommendations.isEmpty {
                report += "=== 改进建议 ===\n"
                for (index, recommendation) in healthReport.recommendations.enumerated() {
                    report += "\(index + 1). \(recommendation)\n"
                }
            }
            
            return .success(report)
            
        case .failure(let error):
            return .failure(error)
        }
    }
}
