//
//  OptimizedSystemMonitorDatabase.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  重构的系统监控数据库管理器 - 基于SQLite的高性能系统监控数据存储解决方案
//

import Foundation
import os.log
import SQLite3

/**
 * 优化的系统监控数据库管理器
 *
 * 这是一个基于SQLite最佳实践重新设计的数据库管理类，专门用于存储和管理系统监控数据。
 * 主要特性：
 * - 高性能：使用WAL模式、预编译语句缓存、批量操作等优化技术
 * - 线程安全：采用串行队列确保数据库操作的线程安全性
 * - 完整的错误处理：定义了详细的错误类型和恢复机制
 * - 自动维护：支持数据清理、压缩、完整性检查等维护功能
 * - 性能监控：内置查询性能监控和健康检查功能
 */
class OptimizedSystemMonitorDatabase {
    // MARK: - 单例和核心属性

    /// 单例实例，确保全局只有一个数据库连接
    static let shared = OptimizedSystemMonitorDatabase()

    /// SQLite数据库连接句柄
    var db: OpaquePointer?

    /// 数据库文件的完整路径
    private let databasePath: String

    /// 文件管理器实例，用于文件操作
    private let fileManager = FileManager.default

    /// 数据库操作专用串行队列，确保线程安全
    /// 使用utility QoS级别，平衡性能和资源消耗
    let dbQueue = DispatchQueue(label: "com.bdoggy.database", qos: .utility)

    // MARK: - 线程安全管理

    /// 队列检查键，用于安全地检测当前是否在数据库队列中执行
    /// 这是防止死锁的关键机制
    private let dbQueueKey = DispatchSpecificKey<Void>()

    // MARK: - 性能优化组件

    /// 预编译语句缓存，避免重复编译相同的SQL语句
    /// Key: 缓存键（通常是SQL语句的标识符）
    /// Value: 预编译的SQLite语句句柄
    private var preparedStatements: [String: OpaquePointer] = [:]

    /// 预编译语句缓存的线程安全锁
    private let statementCacheLock = NSLock()

    // MARK: - 数据库管理组件

    /// 数据库迁移管理器，负责版本升级和数据迁移
    private lazy var migrationManager = DatabaseMigrationManager(database: self)

    // MARK: - 数据库配置常量

    /// 数据库配置参数
    private enum DatabaseConfig {
        /// 当前数据库版本号
        static let currentVersion = 1
        /// 操作失败时的最大重试次数
        static let maxRetryAttempts = 3
        /// 查询超时时间（秒）
        static let queryTimeoutSeconds = 30
        /// 批量操作的默认批次大小
        static let batchSize = 100
    }

    // MARK: - 初始化和生命周期管理

    /**
     * 私有初始化方法（单例模式）
     *
     * 执行以下初始化步骤：
     * 1. 创建应用数据目录
     * 2. 设置数据库文件路径
     * 3. 配置队列标识符
     * 4. 初始化数据库连接和表结构
     */
    private init() {
        // 获取应用支持目录，这是macOS推荐的应用数据存储位置
        guard let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first else {
            fatalError("无法获取应用支持目录 - 这通常表示系统配置问题")
        }

        // 创建BDoggy专用的数据目录
        let appDataURL = appSupportURL.appendingPathComponent("BDoggy")

        // 确保数据目录存在，如果不存在则创建
        do {
            try fileManager.createDirectory(at: appDataURL, withIntermediateDirectories: true)
        } catch {
            Logger.error("创建应用数据目录失败", category: "Database", error: error)
        }

        // 设置数据库文件的完整路径
        databasePath = appDataURL.appendingPathComponent("optimized_system_monitor.db").path

        // 设置队列特定键，用于检测当前线程是否在数据库队列中执行
        // 这是防止死锁的重要机制
        dbQueue.setSpecific(key: dbQueueKey, value: ())

        // 执行数据库初始化
        initializeDatabase()
    }

    /**
     * 析构方法
     * 确保在对象销毁时正确关闭数据库连接和清理资源
     */
    deinit {
        closeDatabase()
    }

    // MARK: - 线程安全和队列管理

    /**
     * 安全的数据库同步执行方法
     *
     * 这是一个关键的线程安全机制，用于防止死锁：
     * - 如果当前已在数据库队列中执行，直接执行代码块
     * - 如果不在数据库队列中，使用dbQueue.sync进行同步执行
     *
     * @param block 要执行的代码块
     * @return 代码块的执行结果
     * @throws 代码块可能抛出的异常
     */
    private func safeDBSync<T>(_ block: () throws -> T) rethrows -> T {
        if DispatchQueue.getSpecific(key: dbQueueKey) != nil {
            // 已在数据库队列中，直接执行避免死锁
            return try block()
        } else {
            // 不在数据库队列中，使用同步执行
            return try dbQueue.sync { try block() }
        }
    }

    /**
     * 检查当前线程是否在数据库队列中执行
     *
     * @return true表示当前在数据库队列中，false表示不在
     */
    private var isOnDatabaseQueue: Bool {
        return DispatchQueue.getSpecific(key: dbQueueKey) != nil
    }

    // MARK: - 数据库连接和配置管理

    /**
     * 初始化数据库连接和配置
     *
     * 执行以下初始化步骤：
     * 1. 打开SQLite数据库连接
     * 2. 配置数据库性能参数
     * 3. 创建必要的表结构
     * 4. 执行数据库迁移（如需要）
     */
    private func initializeDatabase() {
        safeDBSync {
            // 使用sqlite3_open_v2打开数据库，启用完全互斥模式确保线程安全
            if sqlite3_open_v2(databasePath, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_FULLMUTEX, nil) == SQLITE_OK {
                Logger.info("数据库连接成功: \(self.databasePath)", category: "Database")

                // 配置数据库性能参数
                configureDatabase()

                // 创建表结构
                createTablesIfNeeded()

                // 执行数据库迁移
                performMigrationIfNeeded()
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                Logger.error("数据库连接失败: \(errorMessage)", category: "Database")
            }
        }
    }

    /**
     * 配置数据库性能参数
     *
     * 应用SQLite最佳实践配置：
     * - 启用外键约束：确保数据完整性
     * - WAL模式：提高并发性能，减少锁定时间
     * - NORMAL同步模式：平衡性能和数据安全性
     * - 大缓存：提高查询性能
     * - 内存映射I/O：减少系统调用开销
     * - 内存临时存储：提高临时表性能
     */
    private func configureDatabase() {
        var configurationSuccess = true

        // 启用外键约束，确保数据完整性
        if !executeSQL("PRAGMA foreign_keys = ON;", description: "启用外键约束") {
            configurationSuccess = false
        }

        // 设置WAL（Write-Ahead Logging）模式
        // WAL模式允许读写并发，显著提高性能
        if !executeSQL("PRAGMA journal_mode = WAL;", description: "设置WAL模式") {
            configurationSuccess = false
        }

        // 设置NORMAL同步模式
        // 在性能和数据安全性之间取得平衡
        if !executeSQL("PRAGMA synchronous = NORMAL;", description: "设置同步模式") {
            configurationSuccess = false
        }

        // 设置缓存大小为10000页（约40MB）
        // 更大的缓存可以减少磁盘I/O
        if !executeSQL("PRAGMA cache_size = 10000;", description: "设置缓存大小") {
            configurationSuccess = false
        }

        // 启用内存映射I/O（256MB）
        // 减少系统调用开销，提高I/O性能
        if !executeSQL("PRAGMA mmap_size = 268435456;", description: "启用内存映射I/O") {
            configurationSuccess = false
        }

        // 设置临时存储在内存中
        // 提高临时表和排序操作的性能
        if !executeSQL("PRAGMA temp_store = MEMORY;", description: "设置临时存储") {
            configurationSuccess = false
        }

        if configurationSuccess {
            Logger.info("数据库配置完成", category: "Database")
        } else {
            Logger.warning("数据库配置部分失败，但将继续运行", category: "Database")
        }
    }

    /**
     * 根据SQL架构文件创建数据库表结构
     *
     * 该方法执行以下步骤：
     * 1. 读取OptimizedDatabaseSchema.sql文件
     * 2. 解析SQL语句
     * 3. 跳过PRAGMA语句（已在配置阶段执行）
     * 4. 执行表创建、索引创建等DDL语句
     * 5. 记录执行结果和错误信息
     */
    private func createTablesIfNeeded() {
        // 获取数据库架构文件路径
        guard let schemaPath = Bundle.main.path(forResource: "OptimizedDatabaseSchema", ofType: "sql") else {
            Logger.error("找不到数据库架构文件 OptimizedDatabaseSchema.sql", category: "Database")
            return
        }

        do {
            // 读取SQL架构文件内容
            let schemaSQL = try String(contentsOfFile: schemaPath)

            // 解析SQL语句
            let statements = parseSQL(schemaSQL)

            // 统计变量
            var failedStatements: [String] = []
            var allStatements: [String] = []
            var skippedStatements: [String] = []

            // 逐个执行SQL语句
            for statement in statements {
                let trimmedStatement = statement.trimmingCharacters(in: .whitespacesAndNewlines)
                if !trimmedStatement.isEmpty {
                    // 跳过PRAGMA语句，因为它们已经在configureDatabase()中执行
                    if trimmedStatement.uppercased().hasPrefix("PRAGMA") {
                        skippedStatements.append(trimmedStatement)
                        continue
                    }

                    allStatements.append(trimmedStatement)

                    // 执行SQL语句
                    if !executeSQL(trimmedStatement, description: "执行表创建语句: \(getStatementType(trimmedStatement))") {
                        failedStatements.append(trimmedStatement)
                        Logger.error("SQL执行失败: \(trimmedStatement.prefix(100))...", category: "Database")
                    }
                }
            }

            // 记录执行结果统计
            Logger.info("SQL文件解析完成 - 总语句: \(statements.count), 有效语句: \(allStatements.count), 跳过PRAGMA: \(skippedStatements.count)", category: "Database")

            // 根据执行结果记录相应的日志
            if failedStatements.isEmpty {
                Logger.info("数据库表创建完成，成功执行 \(allStatements.count) 个语句", category: "Database")
            } else {
                Logger.warning("部分表创建语句执行失败: \(failedStatements.count)/\(allStatements.count)个", category: "Database")

                // 记录失败语句的详细信息，便于调试
                for (index, failedStatement) in failedStatements.enumerated() {
                    Logger.error("失败语句 \(index + 1): \(failedStatement.prefix(200))...", category: "Database")
                }
            }
        } catch {
            Logger.error("读取数据库架构文件失败", category: "Database", error: error)
        }
    }

    /// 解析SQL文件，使用简化的分隔符逻辑
    private func parseSQL(_ sql: String) -> [String] {
        // 使用新的分隔符 "-- STATEMENT_END --" 进行简单分割
        let statementSeparator = "-- STATEMENT_END --"
        let rawStatements = sql.components(separatedBy: statementSeparator)

        var statements: [String] = []

        for rawStatement in rawStatements {
            // 清理语句：移除前后空白和注释
            let cleanedStatement = cleanStatement(rawStatement)

            if !cleanedStatement.isEmpty {
                statements.append(cleanedStatement)
            }
        }

        Logger.debug("SQL解析完成：从 \(rawStatements.count) 个原始块解析出 \(statements.count) 个有效语句", category: "Database")

        return statements
    }

    /// 清理单个SQL语句，移除注释并规范化空白
    private func cleanStatement(_ statement: String) -> String {
        let lines = statement.components(separatedBy: .newlines)
        var cleanedLines: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // 跳过空行和纯注释行
            if !trimmedLine.isEmpty && !trimmedLine.hasPrefix("--") {
                // 移除行内注释
                let lineWithoutInlineComments = removeInlineComments(from: trimmedLine)
                if !lineWithoutInlineComments.isEmpty {
                    cleanedLines.append(lineWithoutInlineComments)
                }
            }
        }

        // 合并所有行，用空格分隔
        let cleanedStatement = cleanedLines.joined(separator: " ")

        // 移除多余的空格
        let normalizedStatement = cleanedStatement.replacingOccurrences(
            of: "\\s+",
            with: " ",
            options: .regularExpression
        ).trimmingCharacters(in: .whitespacesAndNewlines)

        return normalizedStatement
    }

    /// 移除行内注释，保护字符串字面量中的注释符号
    private func removeInlineComments(from line: String) -> String {
        var result = ""
        var inStringLiteral = false
        var stringDelimiter: Character = "'"
        var i = line.startIndex

        while i < line.endIndex {
            let char = line[i]

            // 处理字符串字面量
            if inStringLiteral {
                result.append(char)
                if char == stringDelimiter {
                    // 检查是否是转义的引号
                    let nextIndex = line.index(after: i)
                    if nextIndex < line.endIndex, line[nextIndex] == stringDelimiter {
                        // 转义的引号，跳过下一个字符
                        i = nextIndex
                        result.append(char)
                    } else {
                        inStringLiteral = false
                    }
                }
                i = line.index(after: i)
                continue
            }

            // 检查字符串字面量开始
            if char == "'" || char == "\"" {
                inStringLiteral = true
                stringDelimiter = char
                result.append(char)
                i = line.index(after: i)
                continue
            }

            // 检查注释开始
            if char == "-" {
                let nextIndex = line.index(after: i)
                if nextIndex < line.endIndex, line[nextIndex] == "-" {
                    // 找到行内注释，停止处理本行剩余部分
                    break
                }
            }

            result.append(char)
            i = line.index(after: i)
        }

        return result.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// 获取SQL语句类型，用于日志记录
    private func getStatementType(_ statement: String) -> String {
        let upperStatement = statement.uppercased()
        if upperStatement.hasPrefix("CREATE TABLE") {
            return "CREATE TABLE"
        } else if upperStatement.hasPrefix("CREATE INDEX") {
            return "CREATE INDEX"
        } else if upperStatement.hasPrefix("CREATE VIEW") {
            return "CREATE VIEW"
        } else if upperStatement.hasPrefix("CREATE TRIGGER") {
            return "CREATE TRIGGER"
        } else if upperStatement.hasPrefix("INSERT") {
            return "INSERT"
        } else if upperStatement.hasPrefix("PRAGMA") {
            return "PRAGMA"
        } else {
            return "OTHER"
        }
    }

    private func performMigrationIfNeeded() {
        switch migrationManager.performMigration() {
        case .success():
            Logger.info("数据库迁移完成", category: "Database")
        case .failure(let error):
            Logger.error("数据库迁移失败", category: "Database", error: error)
        }
    }

    private func closeDatabase() {
        safeDBSync {
            // 清理预编译语句
            statementCacheLock.lock()
            for (_, statement) in preparedStatements {
                sqlite3_finalize(statement)
            }
            preparedStatements.removeAll()
            statementCacheLock.unlock()

            // 关闭数据库连接
            if sqlite3_close(db) == SQLITE_OK {
                Logger.info("数据库连接已关闭", category: "Database")
            } else {
                Logger.error("关闭数据库连接失败", category: "Database")
            }
        }
    }

    // MARK: - SQL执行辅助方法

    @discardableResult
    func executeSQL(_ sql: String, description: String = "") -> Bool {
        guard let db = db else {
            Logger.error("数据库连接不可用", category: "Database")
            return false
        }

        return Logger.measureTime("SQL执行: \(description.isEmpty ? "未知操作" : description)", category: "Database") {
            let result = sqlite3_exec(db, sql, nil, nil, nil)
            if result == SQLITE_OK {
                if !description.isEmpty {
                    Logger.debug("\(description) 成功", category: "Database")
                }
                return true
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                let errorCode = sqlite3_errcode(db)

                Logger.error("SQL执行失败: \(errorMessage) (错误码: \(errorCode))", category: "Database")
                Logger.debug("失败的SQL: \(sql)", category: "Database")

                // 记录特定类型的错误
                recordSQLError(errorCode: errorCode, message: errorMessage, sql: sql)

                return false
            }
        }
    }

    /// 记录SQL错误的详细信息
    private func recordSQLError(errorCode: Int32, message: String, sql: String) {
        let errorType: String
        switch errorCode {
        case SQLITE_BUSY:
            errorType = "数据库忙碌"
        case SQLITE_LOCKED:
            errorType = "数据库锁定"
        case SQLITE_CORRUPT:
            errorType = "数据库损坏"
        case SQLITE_CONSTRAINT:
            errorType = "约束违反"
        case SQLITE_NOMEM:
            errorType = "内存不足"
        case SQLITE_READONLY:
            errorType = "只读数据库"
        case SQLITE_IOERR:
            errorType = "I/O错误"
        default:
            errorType = "未知错误"
        }

        Logger.warning("SQL错误类型: \(errorType) | 错误码: \(errorCode)", category: "Database")

        // 对于严重错误，记录为critical
        if errorCode == SQLITE_CORRUPT || errorCode == SQLITE_IOERR {
            Logger.critical("检测到严重数据库错误: \(errorType)", category: "Database")
        }
    }

    private func prepareStatement(_ sql: String, cacheKey: String? = nil) -> OpaquePointer? {
        guard let db = db else { return nil }

        // 如果有缓存键，尝试从缓存获取
        if let key = cacheKey {
            statementCacheLock.lock()
            if let cachedStatement = preparedStatements[key] {
                statementCacheLock.unlock()
                sqlite3_reset(cachedStatement)
                return cachedStatement
            }
            statementCacheLock.unlock()
        }

        var statement: OpaquePointer?
        let result = sqlite3_prepare_v2(db, sql, -1, &statement, nil)

        if result == SQLITE_OK {
            // 如果有缓存键，存入缓存
            if let key = cacheKey, let stmt = statement {
                statementCacheLock.lock()
                preparedStatements[key] = stmt
                statementCacheLock.unlock()
            }
            return statement
        } else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            Logger.error("预编译语句失败: \(errorMessage), SQL: \(sql)", category: "Database")
            return nil
        }
    }

    // MARK: - 事务管理

    /// 事务状态跟踪
    /// 使用队列检查机制确保线程安全

    func executeInTransaction<T>(_ operation: () throws -> T) -> Result<T, DatabaseError> {
        return safeDBSync {
            guard let db = db else {
                return .failure(.connectionError("数据库连接不可用"))
            }

            // 开始事务
            if sqlite3_exec(db, "BEGIN TRANSACTION;", nil, nil, nil) != SQLITE_OK {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                return .failure(.transactionError("开始事务失败: \(errorMessage)"))
            }

            do {
                let result = try operation()

                // 提交事务
                if sqlite3_exec(db, "COMMIT;", nil, nil, nil) == SQLITE_OK {
                    return .success(result)
                } else {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    sqlite3_exec(db, "ROLLBACK;", nil, nil, nil)
                    return .failure(.transactionError("提交事务失败: \(errorMessage)"))
                }
            } catch {
                // 回滚事务
                sqlite3_exec(db, "ROLLBACK;", nil, nil, nil)
                if let dbError = error as? DatabaseError {
                    return .failure(dbError)
                } else {
                    return .failure(.operationError(error.localizedDescription))
                }
            }
        }
    }

    // MARK: - 错误处理

    enum DatabaseError: Error, LocalizedError {
        case connectionError(String)
        case queryError(String)
        case transactionError(String)
        case operationError(String)
        case dataValidationError(String)
        case insertError(String)
        case updateError(String)
        case deleteError(String)
        case configurationError(String)
        case migrationError(String)
        case integrityError(String)

        var errorDescription: String? {
            switch self {
            case .connectionError(let message):
                return "数据库连接错误: \(message)"
            case .queryError(let message):
                return "查询错误: \(message)"
            case .transactionError(let message):
                return "事务错误: \(message)"
            case .operationError(let message):
                return "操作错误: \(message)"
            case .dataValidationError(let message):
                return "数据验证错误: \(message)"
            case .insertError(let message):
                return "插入错误: \(message)"
            case .updateError(let message):
                return "更新错误: \(message)"
            case .deleteError(let message):
                return "删除错误: \(message)"
            case .configurationError(let message):
                return "配置错误: \(message)"
            case .migrationError(let message):
                return "迁移错误: \(message)"
            case .integrityError(let message):
                return "完整性错误: \(message)"
            }
        }

        /// 错误严重程度
        var severity: ErrorSeverity {
            switch self {
            case .connectionError, .configurationError, .migrationError:
                return .critical
            case .transactionError, .integrityError:
                return .high
            case .insertError, .updateError, .deleteError:
                return .medium
            case .queryError, .operationError, .dataValidationError:
                return .low
            }
        }

        /// 是否可重试
        var isRetryable: Bool {
            switch self {
            case .connectionError, .transactionError, .queryError:
                return true
            case .configurationError, .migrationError, .integrityError:
                return false
            case .insertError, .updateError, .deleteError, .operationError, .dataValidationError:
                return false
            }
        }
    }

    /// 错误严重程度枚举
    enum ErrorSeverity: Int, CaseIterable {
        case low = 1
        case medium = 2
        case high = 3
        case critical = 4

        var description: String {
            switch self {
            case .low:
                return "低"
            case .medium:
                return "中"
            case .high:
                return "高"
            case .critical:
                return "严重"
            }
        }
    }

    // MARK: - 版本管理

    private func getCurrentDatabaseVersion() -> Int {
        let sql = "SELECT version_number FROM schema_versions ORDER BY version_number DESC LIMIT 1;"
        var statement: OpaquePointer?

        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            return 0
        }

        defer { sqlite3_finalize(statement) }

        if sqlite3_step(statement) == SQLITE_ROW {
            return Int(sqlite3_column_int(statement, 0))
        }

        return 0
    }

    private func updateDatabaseVersion(_ version: Int) {
        let sql = "INSERT INTO schema_versions (version_number, description) VALUES (?, ?);"
        var statement: OpaquePointer?

        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("更新数据库版本失败", category: "Database")
            return
        }

        let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
        defer { sqlite3_finalize(statement) }

        sqlite3_bind_int(statement, 1, Int32(version))
        sqlite3_bind_text(statement, 2, ("数据库版本更新到 \(version)" as NSString).utf8String, -1, SQLITE_TRANSIENT)

        if sqlite3_step(statement) == SQLITE_DONE {
            Logger.info("数据库版本已更新到: \(version)", category: "Database")
        }
    }

    // MARK: - 公共接口

    /// 获取数据库路径
    func getDatabasePath() -> String {
        return databasePath
    }

    /// 获取数据库文件大小
    func getDatabaseSize() -> Int64 {
        do {
            let attributes = try fileManager.attributesOfItem(atPath: databasePath)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            Logger.error("获取数据库大小失败", category: "Database", error: error)
            return 0
        }
    }

    /// 执行数据库优化
    func optimizeDatabase() -> Bool {
        return safeDBSync {
            executeSQL("PRAGMA optimize;", description: "数据库优化")
        }
    }

    /// 检查数据库健康状态
    func checkDatabaseHealth() -> Bool {
        return safeDBSync {
            let sql = "PRAGMA integrity_check;"
            var statement: OpaquePointer?

            guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
                return false
            }

            defer { sqlite3_finalize(statement) }

            if sqlite3_step(statement) == SQLITE_ROW {
                let result = String(cString: sqlite3_column_text(statement, 0))
                return result == "ok"
            }

            return false
        }
    }

    /// 执行数据库迁移
    func performDatabaseMigration() -> Result<Void, DatabaseMigrationError> {
        return migrationManager.performMigration()
    }

    /// 清理旧备份文件
    func cleanupOldBackups(keepCount: Int = 5) -> Result<Int, DatabaseMigrationError> {
        return migrationManager.cleanupOldBackups(keepCount: keepCount)
    }

    /// 获取当前数据库版本
    func getCurrentVersion() -> Int {
        return safeDBSync {
            getCurrentDatabaseVersion()
        }
    }
}

// MARK: - 系统信息操作

extension OptimizedSystemMonitorDatabase {
    /// 插入或更新系统信息
    func insertOrUpdateSystemInfo(_ systemInfo: SystemInfo) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT OR REPLACE INTO system_info
                (device_model, cpu_name, cpu_core_count, cpu_architecture, total_memory_mb,
                 total_disk_gb, macos_version, kernel_version, system_serial, recorded_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_system_info") else {
                throw DatabaseError.queryError("准备系统信息插入语句失败")
            }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
            // 绑定参数

            sqlite3_bind_text(statement, 1, (systemInfo.deviceModel as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 2, (systemInfo.cpuName as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(statement, 3, Int32(systemInfo.cpuCoreCount))
            sqlite3_bind_text(statement, 4, (systemInfo.cpuArchitecture as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(statement, 5, Int32(systemInfo.totalMemoryGB * 1024))
            sqlite3_bind_int(statement, 6, Int32(systemInfo.totalDiskGB))
            sqlite3_bind_text(statement, 7, (systemInfo.macOSVersion as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 8, (systemInfo.kernelVersion as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 9, (systemInfo.systemSerial as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int64(statement, 10, Int64(systemInfo.recordedAt.timeIntervalSince1970))

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入系统信息失败: \(errorMessage)")
            }

            Logger.info("系统信息插入成功", category: "Database")
        }
    }

    /// 获取系统信息
    func getSystemInfo() -> Result<SystemInfo?, DatabaseError> {
        return safeDBSync {
            let sql = "SELECT * FROM system_info ORDER BY recorded_at DESC LIMIT 1;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备系统信息查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            if sqlite3_step(statement) == SQLITE_ROW {
                let deviceModel = String(cString: sqlite3_column_text(statement, 1))
                let cpuName = String(cString: sqlite3_column_text(statement, 2))
                let totalMemoryMB = sqlite3_column_int(statement, 5)
                let totalDiskGB = sqlite3_column_int(statement, 6)
                let macOSVersion = String(cString: sqlite3_column_text(statement, 7))
                let recordedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 10)))

                let systemInfo = SystemInfo(
                    deviceModel: deviceModel,
                    cpuName: cpuName,
                    totalMemoryGB: Double(totalMemoryMB) / 1024.0, // 转换回GB
                    totalDiskGB: Double(totalDiskGB),
                    macOSVersion: macOSVersion,
                    recordedAt: recordedAt
                )

                return .success(systemInfo)
            }

            return .success(nil)
        }
    }
}

// MARK: - 应用信息操作

extension OptimizedSystemMonitorDatabase {
    /// 插入或更新应用信息
    func insertOrUpdateAppInfo(bundleID: String, appName: String, appVersion: String? = nil,
                               appCategory: String? = nil, isSystemApp: Bool = false) -> Result<Int64, DatabaseError>
    {
        return executeInTransaction {
            let sql = """
                INSERT OR REPLACE INTO app_info
                (bundle_id, app_name, app_version, app_category, is_system_app)
                VALUES (?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_app_info") else {
                throw DatabaseError.queryError("准备应用信息插入语句失败")
            }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)

            sqlite3_bind_text(statement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 2, (appName as NSString).utf8String, -1, SQLITE_TRANSIENT)

            if let version = appVersion {
                sqlite3_bind_text(statement, 3, (version as NSString).utf8String, -1, SQLITE_TRANSIENT)
            } else {
                sqlite3_bind_null(statement, 3)
            }

            if let category = appCategory {
                sqlite3_bind_text(statement, 4, (category as NSString).utf8String, -1, SQLITE_TRANSIENT)
            } else {
                sqlite3_bind_null(statement, 4)
            }

            sqlite3_bind_int(statement, 5, isSystemApp ? 1 : 0)

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入应用信息失败: \(errorMessage)")
            }

            return sqlite3_last_insert_rowid(db)
        }
    }

    /// 根据Bundle ID获取应用信息
    func getAppInfo(bundleID: String) -> Result<AppInfo?, DatabaseError> {
        return safeDBSync {
            let sql = "SELECT * FROM app_info WHERE bundle_id = ? LIMIT 1;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用信息查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)

            sqlite3_bind_text(statement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)

            if sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let appName = String(cString: sqlite3_column_text(statement, 2))

                let appVersion: String? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 3))
                    }
                    return nil
                }()

                let appCategory: String? = {
                    if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 4))
                    }
                    return nil
                }()

                let isSystemApp = sqlite3_column_int(statement, 7) == 1

                let appInfo = AppInfo(
                    id: id,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: appVersion,
                    appCategory: appCategory,
                    isSystemApp: isSystemApp
                )

                return .success(appInfo)
            }

            return .success(nil)
        }
    }

    /// 获取所有应用信息
    func getAllApps(includeSystemApps: Bool = false) -> Result<[AppInfo], DatabaseError> {
        return safeDBSync {
            var sql = "SELECT * FROM app_info"
            if !includeSystemApps {
                sql += " WHERE is_system_app = 0"
            }
            sql += " ORDER BY app_name;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用列表查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var apps: [AppInfo] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let appName = String(cString: sqlite3_column_text(statement, 2))

                let appVersion: String? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 3))
                    }
                    return nil
                }()

                let appCategory: String? = {
                    if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 4))
                    }
                    return nil
                }()

                let isSystemApp = sqlite3_column_int(statement, 7) == 1

                let appInfo = AppInfo(
                    id: id,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: appVersion,
                    appCategory: appCategory,
                    isSystemApp: isSystemApp
                )

                apps.append(appInfo)
            }

            return .success(apps)
        }
    }
}

// MARK: - CPU指标操作

extension OptimizedSystemMonitorDatabase {
    /// 插入CPU指标数据
    func insertCPUMetrics(_ metrics: CPUMetricsModel) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT INTO cpu_metrics
                (timestamp, usage_percent, user_percent, system_percent, idle_percent,
                 load_avg_1min, load_avg_5min, load_avg_15min, core_count, frequency_mhz)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_cpu_metrics") else {
                throw DatabaseError.queryError("准备CPU指标插入语句失败")
            }

            // 绑定参数（百分比转换为0-10000的整数）
            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.userPercent * 100))
            sqlite3_bind_int(statement, 4, Int32(metrics.systemPercent * 100))
            sqlite3_bind_int(statement, 5, Int32(metrics.idlePercent * 100))

            if let loadAvg1 = metrics.loadAvg1min {
                sqlite3_bind_double(statement, 6, loadAvg1)
            } else {
                sqlite3_bind_null(statement, 6)
            }

            if let loadAvg5 = metrics.loadAvg5min {
                sqlite3_bind_double(statement, 7, loadAvg5)
            } else {
                sqlite3_bind_null(statement, 7)
            }

            if let loadAvg15 = metrics.loadAvg15min {
                sqlite3_bind_double(statement, 8, loadAvg15)
            } else {
                sqlite3_bind_null(statement, 8)
            }

            sqlite3_bind_int(statement, 9, Int32(metrics.coreCount))

            if let frequency = metrics.frequencyMHz {
                sqlite3_bind_int(statement, 10, Int32(frequency))
            } else {
                sqlite3_bind_null(statement, 10)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入CPU指标失败: \(errorMessage)")
            }
        }
    }

    /// 批量插入CPU指标数据（内部方法，不使用事务）
    private func insertCPUMetricsBatchInternal(_ metricsList: [CPUMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO cpu_metrics
            (timestamp, usage_percent, user_percent, system_percent, idle_percent,
             load_avg_1min, load_avg_5min, load_avg_15min, core_count, frequency_mhz)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备CPU指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.userPercent * 100))
            sqlite3_bind_int(statement, 4, Int32(metrics.systemPercent * 100))
            sqlite3_bind_int(statement, 5, Int32(metrics.idlePercent * 100))

            if let loadAvg1 = metrics.loadAvg1min {
                sqlite3_bind_double(statement, 6, loadAvg1)
            } else {
                sqlite3_bind_null(statement, 6)
            }

            if let loadAvg5 = metrics.loadAvg5min {
                sqlite3_bind_double(statement, 7, loadAvg5)
            } else {
                sqlite3_bind_null(statement, 7)
            }

            if let loadAvg15 = metrics.loadAvg15min {
                sqlite3_bind_double(statement, 8, loadAvg15)
            } else {
                sqlite3_bind_null(statement, 8)
            }

            sqlite3_bind_int(statement, 9, Int32(metrics.coreCount))

            if let frequency = metrics.frequencyMHz {
                sqlite3_bind_int(statement, 10, Int32(frequency))
            } else {
                sqlite3_bind_null(statement, 10)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入CPU指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入CPU指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /// 批量插入CPU指标数据（公共接口，使用事务）
    func insertCPUMetricsBatch(_ metricsList: [CPUMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertCPUMetricsBatchInternal(metricsList)
        }
    }

    /// 获取CPU指标数据
    func getCPUMetrics(from startDate: Date, to endDate: Date, limit: Int = 1000) -> Result<[CPUMetricsModel], DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT timestamp, usage_percent, user_percent, system_percent, idle_percent,
                       load_avg_1min, load_avg_5min, load_avg_15min, core_count, frequency_mhz
                FROM cpu_metrics
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备CPU指标查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))
            sqlite3_bind_int(statement, 3, Int32(limit))

            var metrics: [CPUMetricsModel] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))
                let usagePercent = Double(sqlite3_column_int(statement, 1)) / 100.0
                let userPercent = Double(sqlite3_column_int(statement, 2)) / 100.0
                let systemPercent = Double(sqlite3_column_int(statement, 3)) / 100.0
                let idlePercent = Double(sqlite3_column_int(statement, 4)) / 100.0

                let loadAvg1min: Double? = {
                    if sqlite3_column_type(statement, 5) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 5)
                    }
                    return nil
                }()

                let loadAvg5min: Double? = {
                    if sqlite3_column_type(statement, 6) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 6)
                    }
                    return nil
                }()

                let loadAvg15min: Double? = {
                    if sqlite3_column_type(statement, 7) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 7)
                    }
                    return nil
                }()

                let coreCount = Int(sqlite3_column_int(statement, 8))

                let frequencyMHz: Int? = {
                    if sqlite3_column_type(statement, 9) != SQLITE_NULL {
                        return Int(sqlite3_column_int(statement, 9))
                    }
                    return nil
                }()

                let cpuMetrics = CPUMetricsModel(
                    timestamp: timestamp,
                    usagePercent: usagePercent,
                    userPercent: userPercent,
                    systemPercent: systemPercent,
                    idlePercent: idlePercent,
                    loadAvg1min: loadAvg1min,
                    loadAvg5min: loadAvg5min,
                    loadAvg15min: loadAvg15min,
                    coreCount: coreCount,
                    frequencyMHz: frequencyMHz
                )

                metrics.append(cpuMetrics)
            }

            return .success(metrics)
        }
    }
}

// MARK: - 内存指标操作

extension OptimizedSystemMonitorDatabase {
    /// 插入内存指标数据
    func insertMemoryMetrics(_ metrics: MemoryMetricsModel) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT INTO memory_metrics
                (timestamp, usage_percent, used_mb, available_mb, total_mb, active_mb,
                 inactive_mb, wired_mb, compressed_mb, swap_used_mb, swap_total_mb, memory_pressure)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_memory_metrics") else {
                throw DatabaseError.queryError("准备内存指标插入语句失败")
            }

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.usedMB))
            sqlite3_bind_int(statement, 4, Int32(metrics.availableMB))
            sqlite3_bind_int(statement, 5, Int32(metrics.totalMB))
            sqlite3_bind_int(statement, 6, Int32(metrics.activeMB))
            sqlite3_bind_int(statement, 7, Int32(metrics.inactiveMB))
            sqlite3_bind_int(statement, 8, Int32(metrics.wiredMB))
            sqlite3_bind_int(statement, 9, Int32(metrics.compressedMB))
            sqlite3_bind_int(statement, 10, Int32(metrics.swapUsedMB))
            sqlite3_bind_int(statement, 11, Int32(metrics.swapTotalMB))
            sqlite3_bind_int(statement, 12, Int32(metrics.memoryPressure))

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入内存指标失败: \(errorMessage)")
            }
        }
    }

    /// 批量插入内存指标数据（内部方法，不使用事务）
    private func insertMemoryMetricsBatchInternal(_ metricsList: [MemoryMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO memory_metrics
            (timestamp, usage_percent, used_mb, available_mb, total_mb, active_mb,
             inactive_mb, wired_mb, compressed_mb, swap_used_mb, swap_total_mb, memory_pressure)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备内存指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.usedMB))
            sqlite3_bind_int(statement, 4, Int32(metrics.availableMB))
            sqlite3_bind_int(statement, 5, Int32(metrics.totalMB))
            sqlite3_bind_int(statement, 6, Int32(metrics.activeMB))
            sqlite3_bind_int(statement, 7, Int32(metrics.inactiveMB))
            sqlite3_bind_int(statement, 8, Int32(metrics.wiredMB))
            sqlite3_bind_int(statement, 9, Int32(metrics.compressedMB))
            sqlite3_bind_int(statement, 10, Int32(metrics.swapUsedMB))
            sqlite3_bind_int(statement, 11, Int32(metrics.swapTotalMB))
            sqlite3_bind_int(statement, 12, Int32(metrics.memoryPressure))

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入内存指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入内存指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /// 批量插入内存指标数据（公共接口，使用事务）
    func insertMemoryMetricsBatch(_ metricsList: [MemoryMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertMemoryMetricsBatchInternal(metricsList)
        }
    }

    /// 获取内存指标数据
    func getMemoryMetrics(from startDate: Date, to endDate: Date, limit: Int = 1000) -> Result<[MemoryMetricsModel], DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT timestamp, usage_percent, used_mb, available_mb, total_mb, active_mb,
                       inactive_mb, wired_mb, compressed_mb, swap_used_mb, swap_total_mb, memory_pressure
                FROM memory_metrics
                WHERE timestamp BETWEEN ? AND ?
                ORDER BY timestamp ASC
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备内存指标查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))
            sqlite3_bind_int(statement, 3, Int32(limit))

            var metrics: [MemoryMetricsModel] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))
                let usagePercent = Double(sqlite3_column_int(statement, 1)) / 100.0
                let usedMB = Int(sqlite3_column_int(statement, 2))
                let availableMB = Int(sqlite3_column_int(statement, 3))
                let totalMB = Int(sqlite3_column_int(statement, 4))
                let activeMB = Int(sqlite3_column_int(statement, 5))
                let inactiveMB = Int(sqlite3_column_int(statement, 6))
                let wiredMB = Int(sqlite3_column_int(statement, 7))
                let compressedMB = Int(sqlite3_column_int(statement, 8))
                let swapUsedMB = Int(sqlite3_column_int(statement, 9))
                let swapTotalMB = Int(sqlite3_column_int(statement, 10))
                let memoryPressure = Int(sqlite3_column_int(statement, 11))

                let memoryMetrics = MemoryMetricsModel(
                    timestamp: timestamp,
                    usagePercent: usagePercent,
                    usedMB: usedMB,
                    availableMB: availableMB,
                    totalMB: totalMB,
                    activeMB: activeMB,
                    inactiveMB: inactiveMB,
                    wiredMB: wiredMB,
                    compressedMB: compressedMB,
                    swapUsedMB: swapUsedMB,
                    swapTotalMB: swapTotalMB,
                    memoryPressure: memoryPressure
                )

                metrics.append(memoryMetrics)
            }

            return .success(metrics)
        }
    }
}

// MARK: - 应用使用会话操作

extension OptimizedSystemMonitorDatabase {
    /// 插入或更新应用使用会话
    func insertOrUpdateAppUsageSession(_ session: AppUsageSession) -> Result<Int64, DatabaseError> {
        return executeInTransaction {
            if let sessionID = session.id {
                // 更新现有会话
                let sql = """
                    UPDATE app_usage_sessions
                    SET session_end = ?, duration_seconds = ?, peak_cpu_usage = ?, avg_cpu_usage = ?,
                        peak_memory_mb = ?, avg_memory_mb = ?, switch_count = ?, is_active = ?, updated_at = ?
                    WHERE id = ?;
                """

                guard let statement = prepareStatement(sql, cacheKey: "update_app_usage_session") else {
                    throw DatabaseError.queryError("准备应用使用会话更新语句失败")
                }

                if let sessionEnd = session.sessionEnd {
                    sqlite3_bind_int64(statement, 1, Int64(sessionEnd.timeIntervalSince1970))
                } else {
                    sqlite3_bind_null(statement, 1)
                }

                sqlite3_bind_int(statement, 2, Int32(session.durationSeconds))
                sqlite3_bind_int(statement, 3, Int32(session.peakCPUUsage * 100))
                sqlite3_bind_int(statement, 4, Int32(session.avgCPUUsage * 100))
                sqlite3_bind_int(statement, 5, Int32(session.peakMemoryMB))
                sqlite3_bind_int(statement, 6, Int32(session.avgMemoryMB))
                sqlite3_bind_int(statement, 7, Int32(session.switchCount))
                sqlite3_bind_int(statement, 8, session.isActive ? 1 : 0)
                sqlite3_bind_int64(statement, 9, Int64(session.updatedAt.timeIntervalSince1970))
                sqlite3_bind_int64(statement, 10, sessionID)

                if sqlite3_step(statement) != SQLITE_DONE {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("更新应用使用会话失败: \(errorMessage)")
                }

                return sessionID
            } else {
                // 插入新会话
                let sql = """
                    INSERT INTO app_usage_sessions
                    (app_id, session_start, session_end, duration_seconds, peak_cpu_usage, avg_cpu_usage,
                     peak_memory_mb, avg_memory_mb, switch_count, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
                """

                guard let statement = prepareStatement(sql, cacheKey: "insert_app_usage_session") else {
                    throw DatabaseError.queryError("准备应用使用会话插入语句失败")
                }

                sqlite3_bind_int64(statement, 1, session.appID)
                sqlite3_bind_int64(statement, 2, Int64(session.sessionStart.timeIntervalSince1970))

                if let sessionEnd = session.sessionEnd {
                    sqlite3_bind_int64(statement, 3, Int64(sessionEnd.timeIntervalSince1970))
                } else {
                    sqlite3_bind_null(statement, 3)
                }

                sqlite3_bind_int(statement, 4, Int32(session.durationSeconds))
                sqlite3_bind_int(statement, 5, Int32(session.peakCPUUsage * 100))
                sqlite3_bind_int(statement, 6, Int32(session.avgCPUUsage * 100))
                sqlite3_bind_int(statement, 7, Int32(session.peakMemoryMB))
                sqlite3_bind_int(statement, 8, Int32(session.avgMemoryMB))
                sqlite3_bind_int(statement, 9, Int32(session.switchCount))
                sqlite3_bind_int(statement, 10, session.isActive ? 1 : 0)

                if sqlite3_step(statement) != SQLITE_DONE {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("插入应用使用会话失败: \(errorMessage)")
                }

                return sqlite3_last_insert_rowid(db)
            }
        }
    }

    /// 获取活跃的应用使用会话
    func getActiveAppUsageSession(appID: Int64) -> Result<AppUsageSession?, DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT id, app_id, session_start, session_end, duration_seconds, peak_cpu_usage,
                       avg_cpu_usage, peak_memory_mb, avg_memory_mb, switch_count, is_active,
                       created_at, updated_at
                FROM app_usage_sessions
                WHERE app_id = ? AND is_active = 1
                ORDER BY session_start DESC
                LIMIT 1;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备活跃应用会话查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, appID)

            if sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let appID = sqlite3_column_int64(statement, 1)
                let sessionStart = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 2)))

                let sessionEnd: Date? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 3)))
                    }
                    return nil
                }()

                let durationSeconds = Int(sqlite3_column_int(statement, 4))
                let peakCPUUsage = Double(sqlite3_column_int(statement, 5)) / 100.0
                let avgCPUUsage = Double(sqlite3_column_int(statement, 6)) / 100.0
                let peakMemoryMB = Int(sqlite3_column_int(statement, 7))
                let avgMemoryMB = Int(sqlite3_column_int(statement, 8))
                let switchCount = Int(sqlite3_column_int(statement, 9))
                let isActive = sqlite3_column_int(statement, 10) == 1
                let createdAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 11)))
                let updatedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 12)))

                var session = AppUsageSession(appID: appID, sessionStart: sessionStart)
                session = AppUsageSession(
                    id: id,
                    appID: appID,
                    sessionStart: sessionStart,
                    sessionEnd: sessionEnd,
                    durationSeconds: durationSeconds,
                    peakCPUUsage: peakCPUUsage,
                    avgCPUUsage: avgCPUUsage,
                    peakMemoryMB: peakMemoryMB,
                    avgMemoryMB: avgMemoryMB,
                    switchCount: switchCount,
                    isActive: isActive,
                    createdAt: createdAt,
                    updatedAt: updatedAt
                )

                return .success(session)
            }

            return .success(nil)
        }
    }

    /// 获取应用使用统计
    func getAppUsageStats(days: Int = 7, includeSystemApps: Bool = false) -> Result<[AppUsageStats], DatabaseError> {
        return safeDBSync {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))

            var sql = """
                SELECT ai.bundle_id, ai.app_name, ai.app_category,
                       COUNT(aus.id) as session_count,
                       SUM(aus.duration_seconds) as total_duration,
                       AVG(aus.duration_seconds) as avg_duration,
                       MAX(aus.peak_cpu_usage) as max_cpu,
                       AVG(aus.avg_cpu_usage) as avg_cpu,
                       MAX(aus.peak_memory_mb) as max_memory,
                       AVG(aus.avg_memory_mb) as avg_memory,
                       MIN(aus.session_start) as first_used,
                       MAX(aus.session_start) as last_used
                FROM app_info ai
                LEFT JOIN app_usage_sessions aus ON ai.id = aus.app_id
                WHERE aus.session_start >= ?
            """

            if !includeSystemApps {
                sql += " AND ai.is_system_app = 0"
            }

            sql += """
                GROUP BY ai.id, ai.bundle_id, ai.app_name, ai.app_category
                HAVING COUNT(aus.id) > 0
                ORDER BY total_duration DESC;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用统计查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(cutoffDate.timeIntervalSince1970))

            var stats: [AppUsageStats] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let bundleID = String(cString: sqlite3_column_text(statement, 0))
                let appName = String(cString: sqlite3_column_text(statement, 1))

                let appCategory: String? = {
                    if sqlite3_column_type(statement, 2) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 2))
                    }
                    return nil
                }()

                let sessionCount = Int(sqlite3_column_int(statement, 3))
                let totalDuration = Int(sqlite3_column_int(statement, 4))
                let avgDuration = sqlite3_column_double(statement, 5)
                let maxCPU = Double(sqlite3_column_int(statement, 6)) / 100.0
                let avgCPU = Double(sqlite3_column_int(statement, 7)) / 100.0
                let maxMemory = Int(sqlite3_column_int(statement, 8))
                let avgMemory = sqlite3_column_double(statement, 9)
                let firstUsed = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 10)))
                let lastUsed = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 11)))

                let stat = AppUsageStats(
                    bundleID: bundleID,
                    appName: appName,
                    appCategory: appCategory,
                    sessionCount: sessionCount,
                    totalDurationSeconds: totalDuration,
                    avgDurationSeconds: avgDuration,
                    maxCPUUsage: maxCPU,
                    avgCPUUsage: avgCPU,
                    maxMemoryMB: maxMemory,
                    avgMemoryMB: avgMemory,
                    firstUsed: firstUsed,
                    lastUsed: lastUsed
                )

                stats.append(stat)
            }

            return .success(stats)
        }
    }
}

// MARK: - 数据清理和维护

extension OptimizedSystemMonitorDatabase {
    /// 清理指定天数之前的数据
    func cleanupOldData(olderThanDays days: Int) -> Result<Int, DatabaseError> {
        return executeInTransaction {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
            let cutoffTimestamp = Int64(cutoffDate.timeIntervalSince1970)

            let tables = [
                "cpu_metrics",
                "memory_metrics",
                "disk_metrics",
                "battery_metrics",
                "thermal_metrics",
                "network_metrics",
                "process_snapshots",
                "app_usage_details"
            ]

            var totalDeleted = 0

            for table in tables {
                let sql = "DELETE FROM \(table) WHERE timestamp < ?;"

                guard let statement = prepareStatement(sql) else {
                    throw DatabaseError.queryError("准备清理\(table)表语句失败")
                }

                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_DONE {
                    let deletedCount = sqlite3_changes(db)
                    totalDeleted += Int(deletedCount)
                    Logger.info("清理\(table)表: 删除\(deletedCount)条记录", category: "Database")
                } else {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("清理\(table)表失败: \(errorMessage)")
                }

                sqlite3_finalize(statement)
            }

            // 清理应用使用会话（基于session_start）
            let sessionSQL = "DELETE FROM app_usage_sessions WHERE session_start < ?;"
            guard let sessionStatement = prepareStatement(sessionSQL) else {
                throw DatabaseError.queryError("准备清理应用会话表语句失败")
            }

            sqlite3_bind_int64(sessionStatement, 1, cutoffTimestamp)

            if sqlite3_step(sessionStatement) == SQLITE_DONE {
                let deletedCount = sqlite3_changes(db)
                totalDeleted += Int(deletedCount)
                Logger.info("清理app_usage_sessions表: 删除\(deletedCount)条记录", category: "Database")
            }

            sqlite3_finalize(sessionStatement)

            Logger.info("数据清理完成，总共删除\(totalDeleted)条记录", category: "Database")
            return totalDeleted
        }
    }

    /// 清理所有数据（保留系统信息和配置）
    func clearAllData() -> Result<Bool, DatabaseError> {
        return executeInTransaction {
            let tables = [
                "cpu_metrics",
                "memory_metrics",
                "disk_metrics",
                "battery_metrics",
                "thermal_metrics",
                "network_metrics",
                "process_snapshots",
                "app_usage_sessions",
                "app_usage_details",
                "system_events"
            ]

            for table in tables {
                let sql = "DELETE FROM \(table);"
                if !executeSQL(sql, description: "清空\(table)表") {
                    throw DatabaseError.queryError("清空\(table)表失败")
                }
            }

            Logger.info("所有监控数据已清除", category: "Database")
            return true
        }
    }

    /// 压缩数据库
    func vacuumDatabase() -> Result<Void, DatabaseError> {
        return safeDBSync {
            if executeSQL("VACUUM;", description: "压缩数据库") {
                return .success(())
            } else {
                return .failure(.operationError("数据库压缩失败"))
            }
        }
    }

    /// 分析数据库统计信息
    func analyzeDatabase() -> Result<Void, DatabaseError> {
        return safeDBSync {
            if executeSQL("ANALYZE;", description: "分析数据库") {
                return .success(())
            } else {
                return .failure(.operationError("数据库分析失败"))
            }
        }
    }

    /// 获取数据库统计信息
    func getDatabaseStats() -> Result<DatabaseStats, DatabaseError> {
        return safeDBSync {
            var stats = DatabaseStats()

            // 获取各表的记录数
            let tables = [
                "system_info", "app_info", "cpu_metrics", "memory_metrics",
                "disk_metrics", "battery_metrics", "thermal_metrics", "network_metrics",
                "process_snapshots", "app_usage_sessions", "app_usage_details", "system_events"
            ]

            for table in tables {
                let sql = "SELECT COUNT(*) FROM \(table);"
                guard let statement = prepareStatement(sql) else {
                    continue
                }

                if sqlite3_step(statement) == SQLITE_ROW {
                    let count = Int(sqlite3_column_int(statement, 0))
                    stats.tableCounts[table] = count
                }

                sqlite3_finalize(statement)
            }

            // 获取数据库大小
            stats.databaseSizeBytes = getDatabaseSize()

            // 获取最早和最新的数据时间
            let timeRangeSQL = """
                SELECT
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest
                FROM (
                    SELECT timestamp FROM cpu_metrics
                    UNION ALL
                    SELECT timestamp FROM memory_metrics
                    UNION ALL
                    SELECT timestamp FROM disk_metrics
                ) AS all_metrics;
            """

            guard let timeStatement = prepareStatement(timeRangeSQL) else {
                return .success(stats)
            }

            if sqlite3_step(timeStatement) == SQLITE_ROW {
                if sqlite3_column_type(timeStatement, 0) != SQLITE_NULL {
                    stats.earliestDataTime = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(timeStatement, 0)))
                }
                if sqlite3_column_type(timeStatement, 1) != SQLITE_NULL {
                    stats.latestDataTime = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(timeStatement, 1)))
                }
            }

            sqlite3_finalize(timeStatement)

            return .success(stats)
        }
    }
}

// MARK: - 数据库统计信息模型

struct DatabaseStats {
    var tableCounts: [String: Int] = [:]
    var databaseSizeBytes: Int64 = 0
    var earliestDataTime: Date?
    var latestDataTime: Date?

    /// 格式化的数据库大小
    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: databaseSizeBytes)
    }

    /// 数据时间跨度（天数）
    var dataSpanDays: Int? {
        guard let earliest = earliestDataTime,
              let latest = latestDataTime
        else {
            return nil
        }
        return Int(latest.timeIntervalSince(earliest) / (24 * 3600))
    }

    /// 总记录数
    var totalRecords: Int {
        return tableCounts.values.reduce(0, +)
    }
}

// MARK: - 批量数据操作

extension OptimizedSystemMonitorDatabase {
    /// 批量插入混合指标数据
    func insertMetricsBatch(cpuMetrics: [CPUMetricsModel] = [],
                            memoryMetrics: [MemoryMetricsModel] = [],
                            diskMetrics: [DiskMetricsModel] = [],
                            batteryMetrics: [BatteryMetricsModel] = [],
                            thermalMetrics: [ThermalMetricsModel] = [],
                            networkMetrics: [NetworkMetricsModel] = []) -> Result<Void, DatabaseError>
    {
        return executeInTransaction {
            // 批量插入CPU指标
            if !cpuMetrics.isEmpty {
                try insertCPUMetricsBatchInternal(cpuMetrics)
            }

            // 批量插入内存指标
            if !memoryMetrics.isEmpty {
                try insertMemoryMetricsBatchInternal(memoryMetrics)
            }

            // 批量插入磁盘指标
            if !diskMetrics.isEmpty {
                try insertDiskMetricsBatchInternal(diskMetrics)
            }

            // 批量插入电池指标
            if !batteryMetrics.isEmpty {
                try insertBatteryMetricsBatchInternal(batteryMetrics)
            }

            // 批量插入温度指标
            if !thermalMetrics.isEmpty {
                try insertThermalMetricsBatchInternal(thermalMetrics)
            }

            // 批量插入网络指标
            if !networkMetrics.isEmpty {
                try insertNetworkMetricsBatchInternal(networkMetrics)
            }

            let totalCount = cpuMetrics.count + memoryMetrics.count + diskMetrics.count +
                batteryMetrics.count + thermalMetrics.count + networkMetrics.count
            Logger.info("批量插入指标数据成功，总数量: \(totalCount)", category: "Database")
        }
    }

    /// 批量插入磁盘指标数据（内部方法，不使用事务）
    private func insertDiskMetricsBatchInternal(_ metricsList: [DiskMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO disk_metrics
            (timestamp, usage_percent, used_gb, free_gb, total_gb,
             read_ops_per_sec, write_ops_per_sec, read_bytes_per_sec, write_bytes_per_sec)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备磁盘指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(metrics.usagePercent * 100))
            sqlite3_bind_int(statement, 3, Int32(metrics.usedGB))
            sqlite3_bind_int(statement, 4, Int32(metrics.freeGB))
            sqlite3_bind_int(statement, 5, Int32(metrics.totalGB))

            if let readOps = metrics.readOpsPerSec {
                sqlite3_bind_int(statement, 6, Int32(readOps))
            } else {
                sqlite3_bind_null(statement, 6)
            }

            if let writeOps = metrics.writeOpsPerSec {
                sqlite3_bind_int(statement, 7, Int32(writeOps))
            } else {
                sqlite3_bind_null(statement, 7)
            }

            if let readBytes = metrics.readBytesPerSec {
                sqlite3_bind_int(statement, 8, Int32(readBytes))
            } else {
                sqlite3_bind_null(statement, 8)
            }

            if let writeBytes = metrics.writeBytesPerSec {
                sqlite3_bind_int(statement, 9, Int32(writeBytes))
            } else {
                sqlite3_bind_null(statement, 9)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入磁盘指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入磁盘指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /// 批量插入磁盘指标数据（公共接口，使用事务）
    func insertDiskMetricsBatch(_ metricsList: [DiskMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertDiskMetricsBatchInternal(metricsList)
        }
    }

    /// 批量插入电池指标数据（内部方法，不使用事务）
    private func insertBatteryMetricsBatchInternal(_ metricsList: [BatteryMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO battery_metrics
            (timestamp, level_percent, health_percent, cycle_count, power_source_type,
             is_charging, time_remaining_minutes, temperature_celsius, voltage_mv)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备电池指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))

            if let level = metrics.levelPercent {
                sqlite3_bind_int(statement, 2, Int32(level))
            } else {
                sqlite3_bind_null(statement, 2)
            }

            if let health = metrics.healthPercent {
                sqlite3_bind_int(statement, 3, Int32(health))
            } else {
                sqlite3_bind_null(statement, 3)
            }

            if let cycles = metrics.cycleCount {
                sqlite3_bind_int(statement, 4, Int32(cycles))
            } else {
                sqlite3_bind_null(statement, 4)
            }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)

            sqlite3_bind_text(statement, 5, (metrics.powerSourceType as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(statement, 6, metrics.isCharging ? 1 : 0)

            if let timeRemaining = metrics.timeRemainingMinutes {
                sqlite3_bind_int(statement, 7, Int32(timeRemaining))
            } else {
                sqlite3_bind_null(statement, 7)
            }

            if let temperature = metrics.temperatureCelsius {
                sqlite3_bind_double(statement, 8, temperature)
            } else {
                sqlite3_bind_null(statement, 8)
            }

            if let voltage = metrics.voltageMV {
                sqlite3_bind_int(statement, 9, Int32(voltage))
            } else {
                sqlite3_bind_null(statement, 9)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入电池指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入电池指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /// 批量插入电池指标数据（公共接口，使用事务）
    func insertBatteryMetricsBatch(_ metricsList: [BatteryMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertBatteryMetricsBatchInternal(metricsList)
        }
    }

    /// 批量插入温度指标数据（内部方法，不使用事务）
    private func insertThermalMetricsBatchInternal(_ metricsList: [ThermalMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO thermal_metrics
            (timestamp, cpu_temperature_celsius, gpu_temperature_celsius, fan_speed_rpm, thermal_state)
            VALUES (?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备温度指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))

            if let cpuTemp = metrics.cpuTemperatureCelsius {
                sqlite3_bind_double(statement, 2, cpuTemp)
            } else {
                sqlite3_bind_null(statement, 2)
            }

            if let gpuTemp = metrics.gpuTemperatureCelsius {
                sqlite3_bind_double(statement, 3, gpuTemp)
            } else {
                sqlite3_bind_null(statement, 3)
            }

            if let fanSpeed = metrics.fanSpeedRPM {
                sqlite3_bind_int(statement, 4, Int32(fanSpeed))
            } else {
                sqlite3_bind_null(statement, 4)
            }

            if let thermalState = metrics.thermalState {
                let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
                sqlite3_bind_text(statement, 5, (thermalState as NSString).utf8String, -1, SQLITE_TRANSIENT)
            } else {
                sqlite3_bind_null(statement, 5)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入温度指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入温度指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /// 批量插入温度指标数据（公共接口，使用事务）
    func insertThermalMetricsBatch(_ metricsList: [ThermalMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertThermalMetricsBatchInternal(metricsList)
        }
    }

    /// 批量插入网络指标数据（内部方法，不使用事务）
    private func insertNetworkMetricsBatchInternal(_ metricsList: [NetworkMetricsModel]) throws {
        guard !metricsList.isEmpty else { return }

        let sql = """
            INSERT INTO network_metrics
            (timestamp, bytes_in, bytes_out, packets_in, packets_out,
             errors_in, errors_out, interface_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?);
        """

        guard let statement = prepareStatement(sql) else {
            throw DatabaseError.queryError("准备网络指标批量插入语句失败")
        }

        for metrics in metricsList {
            sqlite3_reset(statement)

            sqlite3_bind_int64(statement, 1, Int64(metrics.timestamp.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, metrics.bytesIn)
            sqlite3_bind_int64(statement, 3, metrics.bytesOut)
            sqlite3_bind_int64(statement, 4, metrics.packetsIn)
            sqlite3_bind_int64(statement, 5, metrics.packetsOut)
            sqlite3_bind_int(statement, 6, Int32(metrics.errorsIn))
            sqlite3_bind_int(statement, 7, Int32(metrics.errorsOut))

            if let interfaceName = metrics.interfaceName {
                let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
                sqlite3_bind_text(statement, 8, (interfaceName as NSString).utf8String, -1, SQLITE_TRANSIENT)
            } else {
                sqlite3_bind_null(statement, 8)
            }

            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("批量插入网络指标失败: \(errorMessage)")
            }
        }

        Logger.info("批量插入网络指标成功，数量: \(metricsList.count)", category: "Database")
    }

    /// 批量插入网络指标数据（公共接口，使用事务）
    func insertNetworkMetricsBatch(_ metricsList: [NetworkMetricsModel]) -> Result<Void, DatabaseError> {
        guard !metricsList.isEmpty else { return .success(()) }

        return executeInTransaction {
            try insertNetworkMetricsBatchInternal(metricsList)
        }
    }

    /// 智能合并应用使用记录
    /// 如果同一应用在短时间内（默认5分钟）有多个会话，则合并为一个会话
    func mergeAppUsageSessions(appID: Int64, mergeWindowMinutes: Int = 5) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let mergeWindowSeconds = mergeWindowMinutes * 60

            // 查找需要合并的会话
            let findSQL = """
                SELECT id, session_start, session_end, duration_seconds,
                       peak_cpu_usage, avg_cpu_usage, peak_memory_mb, avg_memory_mb, switch_count
                FROM app_usage_sessions
                WHERE app_id = ? AND is_active = 0
                ORDER BY session_start ASC;
            """

            guard let findStatement = prepareStatement(findSQL) else {
                throw DatabaseError.queryError("准备查找会话语句失败")
            }

            sqlite3_bind_int64(findStatement, 1, appID)

            var sessions: [(id: Int64, start: Date, end: Date?, duration: Int,
                            peakCPU: Double, avgCPU: Double, peakMemory: Int, avgMemory: Int, switchCount: Int)] = []

            while sqlite3_step(findStatement) == SQLITE_ROW {
                let id = sqlite3_column_int64(findStatement, 0)
                let start = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(findStatement, 1)))
                let end: Date? = {
                    if sqlite3_column_type(findStatement, 2) != SQLITE_NULL {
                        return Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(findStatement, 2)))
                    }
                    return nil
                }()
                let duration = Int(sqlite3_column_int(findStatement, 3))
                let peakCPU = Double(sqlite3_column_int(findStatement, 4)) / 100.0
                let avgCPU = Double(sqlite3_column_int(findStatement, 5)) / 100.0
                let peakMemory = Int(sqlite3_column_int(findStatement, 6))
                let avgMemory = Int(sqlite3_column_int(findStatement, 7))
                let switchCount = Int(sqlite3_column_int(findStatement, 8))

                sessions.append((id, start, end, duration, peakCPU, avgCPU, peakMemory, avgMemory, switchCount))
            }

            sqlite3_finalize(findStatement)

            // 合并逻辑
            var mergedSessions: [(sessions: [Int64], start: Date, end: Date?, totalDuration: Int,
                                  peakCPU: Double, avgCPU: Double, peakMemory: Int, avgMemory: Double, totalSwitches: Int)] = []
            var currentGroup: [Int] = []

            for i in 0 ..< sessions.count {
                if currentGroup.isEmpty {
                    currentGroup.append(i)
                } else {
                    guard let lastIndex = currentGroup.last else { continue }
                    let lastSession = sessions[lastIndex]
                    let currentSession = sessions[i]

                    // 检查是否在合并窗口内
                    if let lastEnd = lastSession.end {
                        let timeDiff = currentSession.start.timeIntervalSince(lastEnd)
                        if timeDiff <= TimeInterval(mergeWindowSeconds) {
                            currentGroup.append(i)
                        } else {
                            // 处理当前组
                            if currentGroup.count > 1 {
                                mergedSessions.append(createMergedSession(from: currentGroup, sessions: sessions))
                            }
                            currentGroup = [i]
                        }
                    } else {
                        currentGroup = [i]
                    }
                }
            }

            // 处理最后一组
            if currentGroup.count > 1 {
                mergedSessions.append(createMergedSession(from: currentGroup, sessions: sessions))
            }

            // 执行合并操作
            for merged in mergedSessions {
                // 更新第一个会话
                let firstSessionID = merged.sessions[0]
                let updateSQL = """
                    UPDATE app_usage_sessions
                    SET session_end = ?, duration_seconds = ?, peak_cpu_usage = ?, avg_cpu_usage = ?,
                        peak_memory_mb = ?, avg_memory_mb = ?, switch_count = ?, updated_at = ?
                    WHERE id = ?;
                """

                guard let updateStatement = prepareStatement(updateSQL) else {
                    throw DatabaseError.queryError("准备更新会话语句失败")
                }

                if let end = merged.end {
                    sqlite3_bind_int64(updateStatement, 1, Int64(end.timeIntervalSince1970))
                } else {
                    sqlite3_bind_null(updateStatement, 1)
                }

                sqlite3_bind_int(updateStatement, 2, Int32(merged.totalDuration))
                sqlite3_bind_int(updateStatement, 3, Int32(merged.peakCPU * 100))
                sqlite3_bind_int(updateStatement, 4, Int32(merged.avgCPU * 100))
                sqlite3_bind_int(updateStatement, 5, Int32(merged.peakMemory))
                sqlite3_bind_int(updateStatement, 6, Int32(merged.avgMemory))
                sqlite3_bind_int(updateStatement, 7, Int32(merged.totalSwitches))
                sqlite3_bind_int64(updateStatement, 8, Int64(Date().timeIntervalSince1970))
                sqlite3_bind_int64(updateStatement, 9, firstSessionID)

                if sqlite3_step(updateStatement) != SQLITE_DONE {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("更新合并会话失败: \(errorMessage)")
                }

                sqlite3_finalize(updateStatement)

                // 删除其他会话
                for sessionID in merged.sessions.dropFirst() {
                    let deleteSQL = "DELETE FROM app_usage_sessions WHERE id = ?;"
                    guard let deleteStatement = prepareStatement(deleteSQL) else {
                        throw DatabaseError.queryError("准备删除会话语句失败")
                    }

                    sqlite3_bind_int64(deleteStatement, 1, sessionID)

                    if sqlite3_step(deleteStatement) != SQLITE_DONE {
                        let errorMessage = String(cString: sqlite3_errmsg(db))
                        throw DatabaseError.queryError("删除重复会话失败: \(errorMessage)")
                    }

                    sqlite3_finalize(deleteStatement)
                }
            }

            if !mergedSessions.isEmpty {
                Logger.info("应用使用会话合并完成，合并了\(mergedSessions.count)组会话", category: "Database")
            }
        }
    }

    /// 创建合并后的会话数据
    private func createMergedSession(from indices: [Int],
                                     sessions: [(id: Int64, start: Date, end: Date?, duration: Int,
                                                 peakCPU: Double, avgCPU: Double, peakMemory: Int, avgMemory: Int, switchCount: Int)])
        -> (sessions: [Int64], start: Date, end: Date?, totalDuration: Int, peakCPU: Double, avgCPU: Double, peakMemory: Int, avgMemory: Double, totalSwitches: Int)
    {
        let sessionIDs = indices.map { sessions[$0].id }
        let start = sessions[indices[0]].start
        let end = indices.last.map { sessions[$0].end } ?? nil
        let totalDuration = indices.reduce(0) { $0 + sessions[$1].duration }
        let peakCPU = indices.map { sessions[$0].peakCPU }.max() ?? 0.0
        let avgCPU = indices.map { sessions[$0].avgCPU }.reduce(0, +) / Double(indices.count)
        let peakMemory = indices.map { sessions[$0].peakMemory }.max() ?? 0
        let avgMemory = indices.map { Double(sessions[$0].avgMemory) }.reduce(0, +) / Double(indices.count)
        let totalSwitches = indices.reduce(0) { $0 + sessions[$1].switchCount }

        return (sessionIDs, start, end, totalDuration, peakCPU, avgCPU, peakMemory, avgMemory, totalSwitches)
    }

    /// 批量合并所有应用的使用记录
    func mergeAllAppUsageSessions(mergeWindowMinutes: Int = 5) -> Result<Int, DatabaseError> {
        return executeInTransaction {
            // 获取所有有会话记录的应用ID
            let appsSQL = "SELECT DISTINCT app_id FROM app_usage_sessions WHERE is_active = 0;"
            guard let appsStatement = prepareStatement(appsSQL) else {
                throw DatabaseError.queryError("准备查询应用ID语句失败")
            }

            var appIDs: [Int64] = []
            while sqlite3_step(appsStatement) == SQLITE_ROW {
                appIDs.append(sqlite3_column_int64(appsStatement, 0))
            }

            sqlite3_finalize(appsStatement)

            var totalMerged = 0

            // 为每个应用执行合并
            for appID in appIDs {
                switch mergeAppUsageSessions(appID: appID, mergeWindowMinutes: mergeWindowMinutes) {
                case .success():
                    totalMerged += 1
                case .failure(let error):
                    Logger.error("合并应用\(appID)的会话失败", category: "Database", error: error)
                }
            }

            Logger.info("批量合并完成，处理了\(totalMerged)个应用", category: "Database")
            return totalMerged
        }
    }
}

// MARK: - 高性能查询方法

extension OptimizedSystemMonitorDatabase {
    /// 获取系统性能概览（优化版）
    func getSystemPerformanceOverview(from startDate: Date, to endDate: Date,
                                      sampleInterval: TimeInterval = 300) -> Result<[SystemPerformanceOverview], DatabaseError>
    {
        return safeDBSync {
            // 使用时间分组来减少数据量，提高查询性能
            let intervalSeconds = Int(sampleInterval)

            let sql = """
                WITH time_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(usage_percent) as avg_cpu_usage
                    FROM cpu_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                memory_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(usage_percent) as avg_memory_usage
                    FROM memory_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                disk_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(usage_percent) as avg_disk_usage
                    FROM disk_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                battery_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(level_percent) as avg_battery_level
                    FROM battery_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group
                ),
                thermal_groups AS (
                    SELECT
                        (timestamp / \(intervalSeconds)) * \(intervalSeconds) as time_group,
                        AVG(cpu_temperature_celsius) as avg_cpu_temp,
                        thermal_state
                    FROM thermal_metrics
                    WHERE timestamp BETWEEN ? AND ?
                    GROUP BY time_group, thermal_state
                )
                SELECT
                    tg.time_group as timestamp,
                    tg.avg_cpu_usage,
                    mg.avg_memory_usage,
                    dg.avg_disk_usage,
                    bg.avg_battery_level,
                    thg.avg_cpu_temp,
                    thg.thermal_state
                FROM time_groups tg
                LEFT JOIN memory_groups mg ON tg.time_group = mg.time_group
                LEFT JOIN disk_groups dg ON tg.time_group = dg.time_group
                LEFT JOIN battery_groups bg ON tg.time_group = bg.time_group
                LEFT JOIN thermal_groups thg ON tg.time_group = thg.time_group
                ORDER BY tg.time_group ASC;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备性能概览查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            let startTimestamp = Int64(startDate.timeIntervalSince1970)
            let endTimestamp = Int64(endDate.timeIntervalSince1970)

            // 绑定所有时间参数
            for i in stride(from: 1, through: 10, by: 2) {
                sqlite3_bind_int64(statement, Int32(i), startTimestamp)
                sqlite3_bind_int64(statement, Int32(i + 1), endTimestamp)
            }

            var overviews: [SystemPerformanceOverview] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))

                let cpuUsage: Double? = {
                    if sqlite3_column_type(statement, 1) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 1) / 100.0
                    }
                    return nil
                }()

                let memoryUsage: Double? = {
                    if sqlite3_column_type(statement, 2) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 2) / 100.0
                    }
                    return nil
                }()

                let diskUsage: Double? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 3) / 100.0
                    }
                    return nil
                }()

                let batteryLevel: Int? = {
                    if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                        return Int(sqlite3_column_int(statement, 4))
                    }
                    return nil
                }()

                let cpuTemperature: Double? = {
                    if sqlite3_column_type(statement, 5) != SQLITE_NULL {
                        return sqlite3_column_double(statement, 5)
                    }
                    return nil
                }()

                let thermalState: String? = {
                    if sqlite3_column_type(statement, 6) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 6))
                    }
                    return nil
                }()

                let overview = SystemPerformanceOverview(
                    timestamp: timestamp,
                    cpuUsage: cpuUsage,
                    memoryUsage: memoryUsage,
                    diskUsage: diskUsage,
                    batteryLevel: batteryLevel,
                    cpuTemperature: cpuTemperature,
                    thermalState: thermalState
                )

                overviews.append(overview)
            }

            return .success(overviews)
        }
    }

    /// 获取应用使用趋势（优化版）
    func getAppUsageTrends(days: Int = 7, topN: Int = 10) -> Result<[AppUsageTrend], DatabaseError> {
        return safeDBSync {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))

            let sql = """
                WITH daily_usage AS (
                    SELECT
                        ai.bundle_id,
                        ai.app_name,
                        DATE(aus.session_start, 'unixepoch') as usage_date,
                        SUM(aus.duration_seconds) as daily_duration,
                        COUNT(aus.id) as daily_sessions,
                        AVG(aus.avg_cpu_usage) as daily_avg_cpu,
                        AVG(aus.avg_memory_mb) as daily_avg_memory
                    FROM app_usage_sessions aus
                    JOIN app_info ai ON aus.app_id = ai.id
                    WHERE aus.session_start >= ? AND ai.is_system_app = 0
                    GROUP BY ai.bundle_id, ai.app_name, usage_date
                ),
                app_totals AS (
                    SELECT
                        bundle_id,
                        app_name,
                        SUM(daily_duration) as total_duration,
                        COUNT(DISTINCT usage_date) as active_days
                    FROM daily_usage
                    GROUP BY bundle_id, app_name
                    ORDER BY total_duration DESC
                    LIMIT ?
                )
                SELECT
                    du.bundle_id,
                    du.app_name,
                    du.usage_date,
                    du.daily_duration,
                    du.daily_sessions,
                    du.daily_avg_cpu,
                    du.daily_avg_memory,
                    at.total_duration,
                    at.active_days
                FROM daily_usage du
                JOIN app_totals at ON du.bundle_id = at.bundle_id
                ORDER BY at.total_duration DESC, du.usage_date ASC;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用趋势查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(cutoffDate.timeIntervalSince1970))
            sqlite3_bind_int(statement, 2, Int32(topN))

            var trendsDict: [String: AppUsageTrend] = [:]

            while sqlite3_step(statement) == SQLITE_ROW {
                let bundleID = String(cString: sqlite3_column_text(statement, 0))
                let appName = String(cString: sqlite3_column_text(statement, 1))
                let usageDate = String(cString: sqlite3_column_text(statement, 2))
                let dailyDuration = Int(sqlite3_column_int(statement, 3))
                let dailySessions = Int(sqlite3_column_int(statement, 4))
                let dailyAvgCPU = sqlite3_column_double(statement, 5) / 100.0
                let dailyAvgMemory = sqlite3_column_double(statement, 6)
                let totalDuration = Int(sqlite3_column_int(statement, 7))
                let activeDays = Int(sqlite3_column_int(statement, 8))

                if var trend = trendsDict[bundleID] {
                    trend.dailyData.append(AppUsageTrend.DailyData(
                        date: usageDate,
                        duration: dailyDuration,
                        sessions: dailySessions,
                        avgCPU: dailyAvgCPU,
                        avgMemory: dailyAvgMemory
                    ))
                    trendsDict[bundleID] = trend
                } else {
                    let trend = AppUsageTrend(
                        bundleID: bundleID,
                        appName: appName,
                        totalDuration: totalDuration,
                        activeDays: activeDays,
                        dailyData: [AppUsageTrend.DailyData(
                            date: usageDate,
                            duration: dailyDuration,
                            sessions: dailySessions,
                            avgCPU: dailyAvgCPU,
                            avgMemory: dailyAvgMemory
                        )]
                    )
                    trendsDict[bundleID] = trend
                }
            }

            let trends = Array(trendsDict.values).sorted { $0.totalDuration > $1.totalDuration }
            return .success(trends)
        }
    }

    /// 获取系统资源使用峰值（优化版）
    func getResourceUsagePeaks(days: Int = 7) -> Result<ResourceUsagePeaks, DatabaseError> {
        return safeDBSync {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
            let cutoffTimestamp = Int64(cutoffDate.timeIntervalSince1970)

            var peaks = ResourceUsagePeaks()

            // CPU峰值
            let cpuSQL = """
                SELECT timestamp, usage_percent, load_avg_1min
                FROM cpu_metrics
                WHERE timestamp >= ?
                ORDER BY usage_percent DESC
                LIMIT 1;
            """

            if let statement = prepareStatement(cpuSQL) {
                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    peaks.cpuPeak = ResourceUsagePeaks.Peak(
                        timestamp: Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0))),
                        value: sqlite3_column_double(statement, 1) / 100.0,
                        additionalInfo: ["load_avg": sqlite3_column_double(statement, 2)]
                    )
                }

                sqlite3_finalize(statement)
            }

            // 内存峰值
            let memorySQL = """
                SELECT timestamp, usage_percent, used_mb
                FROM memory_metrics
                WHERE timestamp >= ?
                ORDER BY usage_percent DESC
                LIMIT 1;
            """

            if let statement = prepareStatement(memorySQL) {
                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    peaks.memoryPeak = ResourceUsagePeaks.Peak(
                        timestamp: Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0))),
                        value: sqlite3_column_double(statement, 1) / 100.0,
                        additionalInfo: ["used_mb": Double(sqlite3_column_int(statement, 2))]
                    )
                }

                sqlite3_finalize(statement)
            }

            // 磁盘峰值
            let diskSQL = """
                SELECT timestamp, usage_percent, used_gb
                FROM disk_metrics
                WHERE timestamp >= ?
                ORDER BY usage_percent DESC
                LIMIT 1;
            """

            if let statement = prepareStatement(diskSQL) {
                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    peaks.diskPeak = ResourceUsagePeaks.Peak(
                        timestamp: Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0))),
                        value: sqlite3_column_double(statement, 1) / 100.0,
                        additionalInfo: ["used_gb": Double(sqlite3_column_int(statement, 2))]
                    )
                }

                sqlite3_finalize(statement)
            }

            // 温度峰值
            let thermalSQL = """
                SELECT timestamp, cpu_temperature_celsius
                FROM thermal_metrics
                WHERE timestamp >= ? AND cpu_temperature_celsius IS NOT NULL
                ORDER BY cpu_temperature_celsius DESC
                LIMIT 1;
            """

            if let statement = prepareStatement(thermalSQL) {
                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_ROW {
                    peaks.temperaturePeak = ResourceUsagePeaks.Peak(
                        timestamp: Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0))),
                        value: sqlite3_column_double(statement, 1),
                        additionalInfo: [:]
                    )
                }

                sqlite3_finalize(statement)
            }

            return .success(peaks)
        }
    }

    /// 执行带性能监控的查询
    func executeQueryWithPerformanceMonitoring<T>(_ queryType: String,
                                                  operation: () -> Result<T, DatabaseError>) -> Result<T, DatabaseError>
    {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = operation()
        let executionTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000 // 转换为毫秒

        // 记录查询性能
        let rowCount: Int
        switch result {
        case .success(let data):
            if let array = data as? [Any] {
                rowCount = array.count
            } else {
                rowCount = 1
            }
        case .failure:
            rowCount = 0
        }

        let perfStat = QueryPerformanceStats(
            queryType: queryType,
            executionTimeMs: executionTime,
            rowsReturned: rowCount
        )

        // 如果查询时间超过阈值，记录警告
        if executionTime > 1000 { // 超过1秒
            Logger.warning("慢查询检测: \(queryType) 耗时 \(String(format: "%.2f", executionTime))ms", category: "Database")
        } else {
            Logger.debug("查询性能: \(queryType) 耗时 \(String(format: "%.2f", executionTime))ms，返回 \(rowCount) 行", category: "Database")
        }

        return result
    }

    /// 获取查询性能统计
    func getQueryPerformanceStats() -> [QueryPerformanceStats] {
        // 这里可以实现查询性能统计的存储和检索
        // 为了简化，暂时返回空数组
        return []
    }

    /// 优化数据库查询计划
    func optimizeQueryPlans() -> Result<Void, DatabaseError> {
        return safeDBSync {
            // 更新表统计信息
            if !executeSQL("ANALYZE;", description: "更新表统计信息") {
                return .failure(.operationError("更新表统计信息失败"))
            }

            // 优化查询计划器
            if !executeSQL("PRAGMA optimize;", description: "优化查询计划器") {
                return .failure(.operationError("优化查询计划器失败"))
            }

            Logger.info("数据库查询计划优化完成", category: "Database")
            return .success(())
        }
    }

    /// 获取表大小统计
    func getTableSizeStats() -> Result<[String: Int64], DatabaseError> {
        return safeDBSync {
            var tableSizes: [String: Int64] = [:]

            let tables = [
                "system_info", "app_info", "cpu_metrics", "memory_metrics",
                "disk_metrics", "battery_metrics", "thermal_metrics", "network_metrics",
                "process_snapshots", "app_usage_sessions", "app_usage_details", "system_events"
            ]

            for table in tables {
                let sql = "SELECT COUNT(*) * AVG(LENGTH(CAST(rowid AS TEXT))) FROM \(table);"
                guard let statement = prepareStatement(sql) else {
                    continue
                }

                if sqlite3_step(statement) == SQLITE_ROW {
                    let size = sqlite3_column_int64(statement, 0)
                    tableSizes[table] = size
                }

                sqlite3_finalize(statement)
            }

            return .success(tableSizes)
        }
    }

    /// 获取索引使用统计
    func getIndexUsageStats() -> Result<[String: Any], DatabaseError> {
        return safeDBSync {
            var indexStats: [String: Any] = [:]

            // 获取所有索引信息
            let sql = "SELECT name, tbl_name FROM sqlite_master WHERE type = 'index' AND name NOT LIKE 'sqlite_%';"
            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("获取索引信息失败"))
            }

            var indexes: [(name: String, table: String)] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let indexName = String(cString: sqlite3_column_text(statement, 0))
                let tableName = String(cString: sqlite3_column_text(statement, 1))
                indexes.append((indexName, tableName))
            }

            sqlite3_finalize(statement)

            indexStats["total_indexes"] = indexes.count
            indexStats["indexes_by_table"] = Dictionary(grouping: indexes, by: { $0.table })
                .mapValues { $0.count }

            return .success(indexStats)
        }
    }
}

// MARK: - 数据库健康监控和诊断

extension OptimizedSystemMonitorDatabase {
    /// 执行全面的数据库健康检查
    func performHealthCheck() -> Result<DatabaseHealthReport, DatabaseError> {
        return Logger.measureTime("数据库健康检查", category: "Database") {
            var report = DatabaseHealthReport()

            // 1. 完整性检查
            switch checkDatabaseIntegrity() {
            case .success(let isHealthy):
                report.integrityCheck = isHealthy
                if !isHealthy {
                    report.issues.append("数据库完整性检查失败")
                }
            case .failure(let error):
                report.issues.append("完整性检查错误: \(error.localizedDescription)")
            }

            // 2. 连接状态检查
            report.connectionStatus = (db != nil)
            if !report.connectionStatus {
                report.issues.append("数据库连接不可用")
            }

            // 3. 文件大小检查
            let dbSize = getDatabaseSize()
            report.databaseSizeMB = Double(dbSize) / (1024 * 1024)

            if report.databaseSizeMB > 500 { // 超过500MB
                report.warnings.append("数据库文件较大: \(String(format: "%.1f", report.databaseSizeMB))MB")
            }

            // 4. 表统计检查
            switch getDatabaseStats() {
            case .success(let stats):
                report.totalRecords = stats.totalRecords
                report.tableStats = stats.tableCounts

                // 检查数据分布
                if let metricsCount = stats.tableCounts["cpu_metrics"], metricsCount > 100000 {
                    report.warnings.append("CPU指标数据量较大: \(metricsCount)条记录")
                }

            case .failure(let error):
                report.issues.append("统计信息获取失败: \(error.localizedDescription)")
            }

            // 5. 性能检查
            let performanceResult = checkQueryPerformance()
            report.averageQueryTimeMs = performanceResult.averageTime

            if performanceResult.averageTime > 100 {
                report.warnings.append("查询性能较慢: 平均\(String(format: "%.1f", performanceResult.averageTime))ms")
            }

            // 6. 磁盘空间检查
            if let freeSpace = getAvailableDiskSpace() {
                report.availableDiskSpaceGB = freeSpace

                if freeSpace < 1.0 { // 少于1GB
                    report.issues.append("磁盘空间不足: \(String(format: "%.1f", freeSpace))GB")
                } else if freeSpace < 5.0 { // 少于5GB
                    report.warnings.append("磁盘空间较少: \(String(format: "%.1f", freeSpace))GB")
                }
            }

            // 7. 错误统计
            let errorStats = Logger.getErrorStats(days: 7)
            report.recentErrors = errorStats.totalErrors
            report.recentWarnings = errorStats.totalWarnings

            if errorStats.totalCritical > 0 {
                report.issues.append("检测到\(errorStats.totalCritical)个严重错误")
            }

            // 计算整体健康评分
            report.calculateHealthScore()

            // 记录健康检查结果
            if report.healthScore < 70 {
                Logger.warning("数据库健康评分较低: \(report.healthScore)", category: "Database")
            } else {
                Logger.info("数据库健康检查完成，评分: \(report.healthScore)", category: "Database")
            }

            return .success(report)
        }
    }

    /// 检查数据库完整性
    private func checkDatabaseIntegrity() -> Result<Bool, DatabaseError> {
        return safeDBSync {
            let sql = "PRAGMA integrity_check;"
            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备完整性检查语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var results: [String] = []
            while sqlite3_step(statement) == SQLITE_ROW {
                let result = String(cString: sqlite3_column_text(statement, 0))
                results.append(result)
            }

            let isHealthy = results.count == 1 && results[0] == "ok"

            if !isHealthy {
                Logger.error("数据库完整性检查失败: \(results.joined(separator: ", "))", category: "Database")
            }

            return .success(isHealthy)
        }
    }

    /// 检查查询性能
    private func checkQueryPerformance() -> (averageTime: Double, slowQueries: Int) {
        let testQueries = [
            "SELECT COUNT(*) FROM cpu_metrics WHERE timestamp > strftime('%s', 'now', '-1 day');",
            "SELECT COUNT(*) FROM memory_metrics WHERE timestamp > strftime('%s', 'now', '-1 day');",
            "SELECT COUNT(*) FROM app_usage_sessions WHERE session_start > strftime('%s', 'now', '-1 day');"
        ]

        var totalTime = 0.0
        var slowQueries = 0

        for query in testQueries {
            let startTime = CFAbsoluteTimeGetCurrent()

            if let statement = prepareStatement(query) {
                sqlite3_step(statement)
                sqlite3_finalize(statement)
            }

            let executionTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
            totalTime += executionTime

            if executionTime > 100 {
                slowQueries += 1
            }
        }

        return (totalTime / Double(testQueries.count), slowQueries)
    }

    /// 获取可用磁盘空间
    private func getAvailableDiskSpace() -> Double? {
        do {
            let url = URL(fileURLWithPath: databasePath)
            let values = try url.resourceValues(forKeys: [.volumeAvailableCapacityKey])
            if let capacity = values.volumeAvailableCapacity {
                return Double(capacity) / (1024 * 1024 * 1024) // 转换为GB
            }
        } catch {
            Logger.error("获取磁盘空间失败", category: "Database", error: error)
        }
        return nil
    }

    /// 生成数据库诊断报告
    func generateDiagnosticReport() -> Result<String, DatabaseError> {
        switch performHealthCheck() {
        case .success(let report):
            let reportText = report.generateReportText()
            Logger.info("数据库诊断报告已生成", category: "Database")
            return .success(reportText)
        case .failure(let error):
            Logger.error("生成诊断报告失败", category: "Database", error: error)
            return .failure(error)
        }
    }
}

// MARK: - 高效查询和聚合操作

extension OptimizedSystemMonitorDatabase {
    /// 获取最新的系统指标概览
    func getLatestSystemOverview() -> Result<SystemPerformanceOverview?, DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT
                    c.timestamp,
                    c.usage_percent as cpu_usage,
                    m.usage_percent as memory_usage,
                    d.usage_percent as disk_usage,
                    b.level_percent as battery_level,
                    t.cpu_temperature_celsius,
                    t.thermal_state
                FROM cpu_metrics c
                LEFT JOIN memory_metrics m ON c.timestamp = m.timestamp
                LEFT JOIN disk_metrics d ON c.timestamp = d.timestamp
                LEFT JOIN battery_metrics b ON c.timestamp = b.timestamp
                LEFT JOIN thermal_metrics t ON c.timestamp = t.timestamp
                ORDER BY c.timestamp DESC
                LIMIT 1;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备系统概览查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            if sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))
                let cpuUsage = getOptionalDoubleFromColumn(statement, 1, scale: 100.0)
                let memoryUsage = getOptionalDoubleFromColumn(statement, 2, scale: 100.0)
                let diskUsage = getOptionalDoubleFromColumn(statement, 3, scale: 100.0)
                let batteryLevel = getOptionalIntFromColumn(statement, 4)
                let cpuTemperature = getOptionalDoubleFromColumn(statement, 5)
                let thermalState = getOptionalStringFromColumn(statement, 6)

                let overview = SystemPerformanceOverview(
                    timestamp: timestamp,
                    cpuUsage: cpuUsage,
                    memoryUsage: memoryUsage,
                    diskUsage: diskUsage,
                    batteryLevel: batteryLevel,
                    cpuTemperature: cpuTemperature,
                    thermalState: thermalState
                )

                return .success(overview)
            }

            return .success(nil)
        }
    }

    /// 获取指定时间范围内的平均指标
    func getAverageMetrics(from startDate: Date, to endDate: Date) -> Result<SystemPerformanceOverview?, DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT
                    AVG(c.usage_percent) as avg_cpu,
                    AVG(m.usage_percent) as avg_memory,
                    AVG(d.usage_percent) as avg_disk,
                    AVG(b.level_percent) as avg_battery,
                    AVG(t.cpu_temperature_celsius) as avg_temp,
                    COUNT(*) as sample_count
                FROM cpu_metrics c
                LEFT JOIN memory_metrics m ON c.timestamp = m.timestamp
                LEFT JOIN disk_metrics d ON c.timestamp = d.timestamp
                LEFT JOIN battery_metrics b ON c.timestamp = b.timestamp
                LEFT JOIN thermal_metrics t ON c.timestamp = t.timestamp
                WHERE c.timestamp BETWEEN ? AND ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备平均指标查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))

            if sqlite3_step(statement) == SQLITE_ROW {
                let sampleCount = sqlite3_column_int(statement, 5)
                guard sampleCount > 0 else { return .success(nil) }

                let avgCpu = getOptionalDoubleFromColumn(statement, 0, scale: 100.0)
                let avgMemory = getOptionalDoubleFromColumn(statement, 1, scale: 100.0)
                let avgDisk = getOptionalDoubleFromColumn(statement, 2, scale: 100.0)
                let avgBattery = getOptionalIntFromColumn(statement, 3)
                let avgTemp = getOptionalDoubleFromColumn(statement, 4)

                let overview = SystemPerformanceOverview(
                    timestamp: endDate,
                    cpuUsage: avgCpu,
                    memoryUsage: avgMemory,
                    diskUsage: avgDisk,
                    batteryLevel: avgBattery,
                    cpuTemperature: avgTemp,
                    thermalState: nil
                )

                return .success(overview)
            }

            return .success(nil)
        }
    }

    /// 获取指定时间范围内的峰值指标
    func getPeakMetrics(from startDate: Date, to endDate: Date) -> Result<(maxCpu: Double, maxMemory: Double, maxTemp: Double?), DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT
                    MAX(c.usage_percent) as max_cpu,
                    MAX(m.usage_percent) as max_memory,
                    MAX(t.cpu_temperature_celsius) as max_temp
                FROM cpu_metrics c
                LEFT JOIN memory_metrics m ON c.timestamp = m.timestamp
                LEFT JOIN thermal_metrics t ON c.timestamp = t.timestamp
                WHERE c.timestamp BETWEEN ? AND ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备峰值指标查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))

            if sqlite3_step(statement) == SQLITE_ROW {
                let maxCpu = sqlite3_column_double(statement, 0) / 100.0
                let maxMemory = sqlite3_column_double(statement, 1) / 100.0
                let maxTemp = getOptionalDoubleFromColumn(statement, 2)

                return .success((maxCpu, maxMemory, maxTemp))
            }

            return .failure(.queryError("获取峰值指标失败"))
        }
    }

    /// 获取时间序列数据（用于图表显示）
    func getTimeSeriesData(
        metricType: String,
        from startDate: Date,
        to endDate: Date,
        interval: TimeInterval = 300 // 5分钟间隔
    ) -> Result<[(timestamp: Date, value: Double)], DatabaseError> {
        return safeDBSync {
            let tableName: String
            let valueColumn: String

            switch metricType.lowercased() {
            case "cpu":
                tableName = "cpu_metrics"
                valueColumn = "usage_percent"
            case "memory":
                tableName = "memory_metrics"
                valueColumn = "usage_percent"
            case "disk":
                tableName = "disk_metrics"
                valueColumn = "usage_percent"
            case "battery":
                tableName = "battery_metrics"
                valueColumn = "level_percent"
            default:
                return .failure(.queryError("不支持的指标类型: \(metricType)"))
            }

            let sql = """
                SELECT
                    (timestamp / \(Int(interval))) * \(Int(interval)) as time_bucket,
                    AVG(\(valueColumn)) as avg_value
                FROM \(tableName)
                WHERE timestamp BETWEEN ? AND ?
                GROUP BY time_bucket
                ORDER BY time_bucket;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备时间序列查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))

            var results: [(timestamp: Date, value: Double)] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let timestamp = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 0)))
                let value = sqlite3_column_double(statement, 1)
                let normalizedValue = (metricType.lowercased() == "battery") ? value : value / 100.0

                results.append((timestamp, normalizedValue))
            }

            return .success(results)
        }
    }

    /// 获取应用使用排行榜
    func getTopApps(
        from startDate: Date,
        to endDate: Date,
        limit: Int = 10,
        sortBy: String = "duration" // duration, cpu, memory
    ) -> Result<[(appName: String, bundleID: String, value: Double)], DatabaseError> {
        return safeDBSync {
            let orderColumn: String
            switch sortBy.lowercased() {
            case "cpu":
                orderColumn = "AVG(s.avg_cpu_usage)"
            case "memory":
                orderColumn = "AVG(s.avg_memory_mb)"
            default:
                orderColumn = "SUM(s.duration_seconds)"
            }

            let sql = """
                SELECT
                    a.app_name,
                    a.bundle_id,
                    \(orderColumn) as sort_value
                FROM app_usage_sessions s
                JOIN app_info a ON s.app_id = a.id
                WHERE s.session_start BETWEEN ? AND ?
                GROUP BY a.id, a.app_name, a.bundle_id
                ORDER BY sort_value DESC
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用排行查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))
            sqlite3_bind_int(statement, 3, Int32(limit))

            var results: [(appName: String, bundleID: String, value: Double)] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let appName = String(cString: sqlite3_column_text(statement, 0))
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let value = sqlite3_column_double(statement, 2)

                results.append((appName, bundleID, value))
            }

            return .success(results)
        }
    }
}

// MARK: - 应用使用会话管理

extension OptimizedSystemMonitorDatabase {
    /// 获取或创建应用信息
    func getOrCreateAppInfo(bundleID: String, appName: String) -> Result<AppInfo, DatabaseError> {
        return safeDBSync {
            // 首先尝试获取现有的应用信息
            let selectSQL = """
                SELECT id, bundle_id, app_name, app_version, app_category, vendor_name,
                       install_date, last_updated, is_system_app, created_at, updated_at
                FROM app_info
                WHERE bundle_id = ?;
            """

            guard let selectStatement = prepareStatement(selectSQL) else {
                return .failure(.queryError("准备查询应用信息语句失败"))
            }

            defer { sqlite3_finalize(selectStatement) }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)

            sqlite3_bind_text(selectStatement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)

            if sqlite3_step(selectStatement) == SQLITE_ROW {
                // 应用信息已存在，返回现有记录
                let id = sqlite3_column_int64(selectStatement, 0)
                let bundleID = String(cString: sqlite3_column_text(selectStatement, 1))
                let appName = String(cString: sqlite3_column_text(selectStatement, 2))

                let appVersion = getOptionalStringFromColumn(selectStatement, 3)
                let appCategory = getOptionalStringFromColumn(selectStatement, 4)
                let vendorName = getOptionalStringFromColumn(selectStatement, 5)
                let installDate = getOptionalDateFromColumn(selectStatement, 6)
                let lastUpdated = getOptionalDateFromColumn(selectStatement, 7)

                let isSystemApp = sqlite3_column_int(selectStatement, 8) != 0
                let createdAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(selectStatement, 9)))
                let updatedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(selectStatement, 10)))

                let appInfo = AppInfo(
                    id: id,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: appVersion,
                    appCategory: appCategory,
                    vendorName: vendorName,
                    installDate: installDate,
                    lastUpdated: lastUpdated,
                    isSystemApp: isSystemApp,
                    createdAt: createdAt,
                    updatedAt: updatedAt
                )

                return .success(appInfo)
            }

            // 应用信息不存在，创建新记录
            let insertSQL = """
                INSERT INTO app_info (bundle_id, app_name, is_system_app, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?);
            """

            guard let insertStatement = prepareStatement(insertSQL) else {
                return .failure(.insertError("准备插入应用信息语句失败"))
            }

            defer { sqlite3_finalize(insertStatement) }

            let now = Date()
            let isSystemApp = bundleID.hasPrefix("com.apple.") || bundleID.contains("system")

            sqlite3_bind_text(insertStatement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(insertStatement, 2, (appName as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(insertStatement, 3, isSystemApp ? 1 : 0)
            sqlite3_bind_int64(insertStatement, 4, Int64(now.timeIntervalSince1970))
            sqlite3_bind_int64(insertStatement, 5, Int64(now.timeIntervalSince1970))

            if sqlite3_step(insertStatement) == SQLITE_DONE {
                let newID = sqlite3_last_insert_rowid(db)

                let newAppInfo = AppInfo(
                    id: newID,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: nil,
                    appCategory: nil,
                    vendorName: nil,
                    installDate: nil,
                    lastUpdated: nil,
                    isSystemApp: isSystemApp,
                    createdAt: now,
                    updatedAt: now
                )

                return .success(newAppInfo)
            } else {
                return .failure(.insertError("插入应用信息失败"))
            }
        }
    }

    /// 插入应用使用会话
    func insertAppUsageSession(_ session: AppUsageSession) -> Result<Int64, DatabaseError> {
        return safeDBSync {
            let sql = """
                INSERT INTO app_usage_sessions
                (app_id, session_start, session_end, duration_seconds, peak_cpu_usage,
                 avg_cpu_usage, peak_memory_mb, avg_memory_mb, switch_count, is_active,
                 created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.insertError("准备插入应用使用会话语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, session.appID)
            sqlite3_bind_int64(statement, 2, Int64(session.sessionStart.timeIntervalSince1970))

            if let sessionEnd = session.sessionEnd {
                sqlite3_bind_int64(statement, 3, Int64(sessionEnd.timeIntervalSince1970))
            } else {
                sqlite3_bind_null(statement, 3)
            }

            sqlite3_bind_int(statement, 4, Int32(session.durationSeconds))
            sqlite3_bind_int(statement, 5, Int32(session.peakCPUUsage * 100)) // 转换为0-10000
            sqlite3_bind_int(statement, 6, Int32(session.avgCPUUsage * 100)) // 转换为0-10000
            sqlite3_bind_int(statement, 7, Int32(session.peakMemoryMB))
            sqlite3_bind_int(statement, 8, Int32(session.avgMemoryMB))
            sqlite3_bind_int(statement, 9, Int32(session.switchCount))
            sqlite3_bind_int(statement, 10, session.isActive ? 1 : 0)
            sqlite3_bind_int64(statement, 11, Int64(session.createdAt.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 12, Int64(session.updatedAt.timeIntervalSince1970))

            if sqlite3_step(statement) == SQLITE_DONE {
                let sessionID = sqlite3_last_insert_rowid(db)
                return .success(sessionID)
            } else {
                return .failure(.insertError("插入应用使用会话失败"))
            }
        }
    }

    /// 批量插入应用使用详细数据
    func insertAppUsageDetails(_ details: [AppUsageDetail]) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT INTO app_usage_details (session_id, timestamp, cpu_usage, memory_usage_mb, created_at)
                VALUES (?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql) else {
                throw DatabaseError.insertError("准备插入应用使用详细数据语句失败")
            }

            defer { sqlite3_finalize(statement) }

            for detail in details {
                sqlite3_reset(statement)

                sqlite3_bind_int64(statement, 1, detail.sessionID)
                sqlite3_bind_int64(statement, 2, Int64(detail.timestamp.timeIntervalSince1970))
                sqlite3_bind_int(statement, 3, Int32(detail.cpuUsage * 100)) // 转换为0-10000
                sqlite3_bind_int(statement, 4, Int32(detail.memoryUsageMB))
                sqlite3_bind_int64(statement, 5, Int64(detail.createdAt.timeIntervalSince1970))

                if sqlite3_step(statement) != SQLITE_DONE {
                    throw DatabaseError.insertError("插入应用使用详细数据失败")
                }
            }

            Logger.info("批量插入应用使用详细数据成功，数量: \(details.count)", category: "Database")
        }
    }

    /// 获取应用使用聚合统计数据
    func getAppUsageAggregatedStats(
        bundleID: String? = nil,
        from startDate: Date,
        to endDate: Date
    ) -> Result<[AppUsageStats], DatabaseError> {
        return safeDBSync {
            var sql = """
                SELECT
                    ai.bundle_id,
                    ai.app_name,
                    ai.app_category,
                    COUNT(aus.id) as session_count,
                    SUM(aus.duration_seconds) as total_duration_seconds,
                    AVG(aus.duration_seconds) as avg_duration_seconds,
                    MAX(aus.peak_cpu_usage) as max_cpu_usage,
                    AVG(aus.avg_cpu_usage) as avg_cpu_usage,
                    MAX(aus.peak_memory_mb) as max_memory_mb,
                    AVG(aus.avg_memory_mb) as avg_memory_mb,
                    MIN(aus.session_start) as first_used,
                    MAX(aus.session_start) as last_used
                FROM app_info ai
                JOIN app_usage_sessions aus ON ai.id = aus.app_id
                WHERE aus.session_start BETWEEN ? AND ?
            """

            if let bundleID = bundleID {
                sql += " AND ai.bundle_id = ?"
            }

            sql += """
                GROUP BY ai.id, ai.bundle_id, ai.app_name, ai.app_category
                ORDER BY total_duration_seconds DESC;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用聚合统计查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))

            if let bundleID = bundleID {
                
                let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
                sqlite3_bind_text(statement, 3, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)
            }

            var stats: [AppUsageStats] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let bundleID = String(cString: sqlite3_column_text(statement, 0))
                let appName = String(cString: sqlite3_column_text(statement, 1))
                let appCategory = getOptionalStringFromColumn(statement, 2)

                let sessionCount = Int(sqlite3_column_int(statement, 3))
                let totalDurationSeconds = Int(sqlite3_column_int(statement, 4))
                let avgDurationSeconds = sqlite3_column_double(statement, 5)
                let maxCPUUsage = sqlite3_column_double(statement, 6) / 100.0 // 转换回0-100
                let avgCPUUsage = sqlite3_column_double(statement, 7) / 100.0
                let maxMemoryMB = Int(sqlite3_column_int(statement, 8))
                let avgMemoryMB = sqlite3_column_double(statement, 9)
                let firstUsed = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 10)))
                let lastUsed = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 11)))

                let stat = AppUsageStats(
                    bundleID: bundleID,
                    appName: appName,
                    appCategory: appCategory,
                    sessionCount: sessionCount,
                    totalDurationSeconds: totalDurationSeconds,
                    avgDurationSeconds: avgDurationSeconds,
                    maxCPUUsage: maxCPUUsage,
                    avgCPUUsage: avgCPUUsage,
                    maxMemoryMB: maxMemoryMB,
                    avgMemoryMB: avgMemoryMB,
                    firstUsed: firstUsed,
                    lastUsed: lastUsed
                )

                stats.append(stat)
            }

            return .success(stats)
        }
    }

    /// 获取应用使用趋势数据
    func getAppUsageTrends(
        bundleIDs: [String]? = nil,
        from startDate: Date,
        to endDate: Date,
        groupBy: String = "day" // day, week, month
    ) -> Result<[AppUsageTrend], DatabaseError> {
        return safeDBSync {
            let dateFormat: String
            let dateGrouping: String

            switch groupBy.lowercased() {
            case "week":
                dateFormat = "%Y-W%W"
                dateGrouping = "strftime('%Y-W%W', datetime(aus.session_start, 'unixepoch'))"
            case "month":
                dateFormat = "%Y-%m"
                dateGrouping = "strftime('%Y-%m', datetime(aus.session_start, 'unixepoch'))"
            default: // day
                dateFormat = "%Y-%m-%d"
                dateGrouping = "strftime('%Y-%m-%d', datetime(aus.session_start, 'unixepoch'))"
            }

            var sql = """
                SELECT
                    ai.bundle_id,
                    ai.app_name,
                    \(dateGrouping) as date_group,
                    SUM(aus.duration_seconds) as duration,
                    COUNT(aus.id) as sessions,
                    AVG(aus.avg_cpu_usage) as avg_cpu,
                    AVG(aus.avg_memory_mb) as avg_memory
                FROM app_info ai
                JOIN app_usage_sessions aus ON ai.id = aus.app_id
                WHERE aus.session_start BETWEEN ? AND ?
            """

            if let bundleIDs = bundleIDs, !bundleIDs.isEmpty {
                let placeholders = bundleIDs.map { _ in "?" }.joined(separator: ",")
                sql += " AND ai.bundle_id IN (\(placeholders))"
            }

            sql += """
                GROUP BY ai.id, ai.bundle_id, ai.app_name, date_group
                ORDER BY ai.bundle_id, date_group;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用使用趋势查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var paramIndex: Int32 = 1
            sqlite3_bind_int64(statement, paramIndex, Int64(startDate.timeIntervalSince1970))
            paramIndex += 1
            sqlite3_bind_int64(statement, paramIndex, Int64(endDate.timeIntervalSince1970))
            paramIndex += 1
        
            if let bundleIDs = bundleIDs {
                let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
                for bundleID in bundleIDs {
                    sqlite3_bind_text(statement, paramIndex, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)
                    paramIndex += 1
                }
            }

            var trendsDict: [String: AppUsageTrend] = [:]

            while sqlite3_step(statement) == SQLITE_ROW {
                let bundleID = String(cString: sqlite3_column_text(statement, 0))
                let appName = String(cString: sqlite3_column_text(statement, 1))
                let dateGroup = String(cString: sqlite3_column_text(statement, 2))
                let duration = Int(sqlite3_column_int(statement, 3))
                let sessions = Int(sqlite3_column_int(statement, 4))
                let avgCPU = sqlite3_column_double(statement, 5) / 100.0
                let avgMemory = sqlite3_column_double(statement, 6)

                let dailyData = AppUsageTrend.DailyData(
                    date: dateGroup,
                    duration: duration,
                    sessions: sessions,
                    avgCPU: avgCPU,
                    avgMemory: avgMemory
                )

                if var trend = trendsDict[bundleID] {
                    trend.dailyData.append(dailyData)
                    trendsDict[bundleID] = trend
                } else {
                    let newTrend = AppUsageTrend(
                        bundleID: bundleID,
                        appName: appName,
                        totalDuration: duration,
                        activeDays: 1,
                        dailyData: [dailyData]
                    )
                    trendsDict[bundleID] = newTrend
                }
            }

            // 计算总计数据并创建最终的趋势对象
            var trends: [AppUsageTrend] = []
            for (_, trend) in trendsDict {
                // 计算正确的总时长和活跃天数
                let calculatedTotalDuration = trend.dailyData.reduce(0) { $0 + $1.duration }
                let calculatedActiveDays = trend.dailyData.count

                // 创建新的AppUsageTrend实例，包含正确的计算值
                let finalTrend = AppUsageTrend(
                    bundleID: trend.bundleID,
                    appName: trend.appName,
                    totalDuration: calculatedTotalDuration,
                    activeDays: calculatedActiveDays,
                    dailyData: trend.dailyData
                )

                trends.append(finalTrend)
            }

            return .success(trends)
        }
    }
}

// MARK: - 数据库性能优化和维护

extension OptimizedSystemMonitorDatabase {
    /// 优化数据库性能
    func optimizeDatabasePerformance() -> Result<String, DatabaseError> {
        return safeDBSync {
            var optimizationResults: [String] = []

            // 1. 分析数据库
            Logger.info("开始分析数据库...", category: "Database")
            if sqlite3_exec(db, "ANALYZE;", nil, nil, nil) == SQLITE_OK {
                optimizationResults.append("✅ 数据库分析完成")
            } else {
                optimizationResults.append("❌ 数据库分析失败")
            }

            // 2. 重建索引
            Logger.info("重建数据库索引...", category: "Database")
            if sqlite3_exec(db, "REINDEX;", nil, nil, nil) == SQLITE_OK {
                optimizationResults.append("✅ 索引重建完成")
            } else {
                optimizationResults.append("❌ 索引重建失败")
            }

            // 3. 清理碎片
            Logger.info("清理数据库碎片...", category: "Database")
            if sqlite3_exec(db, "VACUUM;", nil, nil, nil) == SQLITE_OK {
                optimizationResults.append("✅ 数据库碎片清理完成")
            } else {
                optimizationResults.append("❌ 数据库碎片清理失败")
            }

            // 4. 更新统计信息
            let updateStatsSQL = """
                UPDATE sqlite_stat1 SET stat = (
                    SELECT COUNT(*) FROM cpu_metrics
                ) WHERE tbl = 'cpu_metrics';
            """

            if sqlite3_exec(db, updateStatsSQL, nil, nil, nil) == SQLITE_OK {
                optimizationResults.append("✅ 统计信息更新完成")
            } else {
                optimizationResults.append("⚠️ 统计信息更新跳过")
            }

            let report = optimizationResults.joined(separator: "\n")
            Logger.info("数据库优化完成:\n\(report)", category: "Database")

            return .success(report)
        }
    }

    /// 清理过期数据
    func cleanupExpiredData(retentionDays: Int = 30) -> Result<Int, DatabaseError> {
        return safeDBSync {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(retentionDays * 24 * 3600))
            let cutoffTimestamp = Int64(cutoffDate.timeIntervalSince1970)

            var totalDeleted = 0

            // 开始事务
            guard sqlite3_exec(db, "BEGIN TRANSACTION", nil, nil, nil) == SQLITE_OK else {
                return .failure(.transactionError("开始清理事务失败"))
            }

            // 清理各类指标数据
            let tables = [
                "cpu_metrics",
                "memory_metrics",
                "disk_metrics",
                "battery_metrics",
                "thermal_metrics",
                "network_metrics"
            ]

            for table in tables {
                let sql = "DELETE FROM \(table) WHERE timestamp < ?;"

                guard let statement = prepareStatement(sql) else {
                    sqlite3_exec(db, "ROLLBACK", nil, nil, nil)
                    return .failure(.deleteError("准备清理\(table)语句失败"))
                }

                defer { sqlite3_finalize(statement) }

                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_DONE {
                    let deleted = sqlite3_changes(db)
                    totalDeleted += Int(deleted)
                    Logger.debug("清理\(table): \(deleted)条记录", category: "Database")
                } else {
                    sqlite3_exec(db, "ROLLBACK", nil, nil, nil)
                    return .failure(.deleteError("清理\(table)失败"))
                }
            }

            // 清理应用使用会话数据
            let sessionSQL = "DELETE FROM app_usage_sessions WHERE session_start < ?;"
            guard let sessionStatement = prepareStatement(sessionSQL) else {
                sqlite3_exec(db, "ROLLBACK", nil, nil, nil)
                return .failure(.deleteError("准备清理应用会话语句失败"))
            }

            defer { sqlite3_finalize(sessionStatement) }

            sqlite3_bind_int64(sessionStatement, 1, cutoffTimestamp)

            if sqlite3_step(sessionStatement) == SQLITE_DONE {
                let deleted = sqlite3_changes(db)
                totalDeleted += Int(deleted)
                Logger.debug("清理应用会话: \(deleted)条记录", category: "Database")
            } else {
                sqlite3_exec(db, "ROLLBACK", nil, nil, nil)
                return .failure(.deleteError("清理应用会话失败"))
            }

            // 提交事务
            guard sqlite3_exec(db, "COMMIT", nil, nil, nil) == SQLITE_OK else {
                sqlite3_exec(db, "ROLLBACK", nil, nil, nil)
                return .failure(.transactionError("提交清理事务失败"))
            }

            Logger.info("数据清理完成，共删除 \(totalDeleted) 条记录", category: "Database")

            return .success(totalDeleted)
        }
    }

    /// 获取数据库大小信息
    func getDatabaseSizeInfo() -> Result<DatabaseSizeInfo, DatabaseError> {
        return safeDBSync {
            // 获取数据库文件大小
            let fileSize = getDatabaseFileSize()

            // 获取各表的记录数和大小
            var tableStats: [TableSizeInfo] = []

            let tables = [
                "cpu_metrics", "memory_metrics", "disk_metrics",
                "battery_metrics", "thermal_metrics", "network_metrics",
                "app_info", "app_usage_sessions", "app_usage_details"
            ]

            for tableName in tables {
                let countSQL = "SELECT COUNT(*) FROM \(tableName);"
                guard let statement = prepareStatement(countSQL) else {
                    continue
                }

                defer { sqlite3_finalize(statement) }

                if sqlite3_step(statement) == SQLITE_ROW {
                    let count = sqlite3_column_int64(statement, 0)

                    let tableInfo = TableSizeInfo(
                        tableName: tableName,
                        recordCount: Int(count),
                        estimatedSizeBytes: Int(count) * getEstimatedRecordSize(for: tableName)
                    )

                    tableStats.append(tableInfo)
                }
            }

            let sizeInfo = DatabaseSizeInfo(
                totalFileSizeBytes: fileSize,
                tableStats: tableStats,
                lastUpdated: Date()
            )

            return .success(sizeInfo)
        }
    }

    /// 获取数据库文件大小
    private func getDatabaseFileSize() -> Int {
        let fileManager = FileManager.default
        do {
            let attributes = try fileManager.attributesOfItem(atPath: databasePath)
            return attributes[.size] as? Int ?? 0
        } catch {
            Logger.error("获取数据库文件大小失败: \(error)", category: "Database")
            return 0
        }
    }

    /// 估算记录大小
    private func getEstimatedRecordSize(for tableName: String) -> Int {
        switch tableName {
        case "cpu_metrics":
            return 120 // 估算每条CPU记录约120字节
        case "memory_metrics":
            return 140 // 估算每条内存记录约140字节
        case "disk_metrics":
            return 100
        case "battery_metrics":
            return 80
        case "thermal_metrics":
            return 60
        case "network_metrics":
            return 100
        case "app_info":
            return 200
        case "app_usage_sessions":
            return 150
        case "app_usage_details":
            return 50
        default:
            return 100
        }
    }

    /// 执行数据库完整性检查
    func performIntegrityCheck() -> Result<Bool, DatabaseError> {
        return safeDBSync {
            let sql = "PRAGMA integrity_check;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备完整性检查语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var isHealthy = true
            var issues: [String] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let result = String(cString: sqlite3_column_text(statement, 0))
                if result != "ok" {
                    isHealthy = false
                    issues.append(result)
                }
            }

            if isHealthy {
                Logger.info("数据库完整性检查通过", category: "Database")
            } else {
                Logger.warning("数据库完整性检查发现问题: \(issues.joined(separator: ", "))", category: "Database")
            }

            return .success(isHealthy)
        }
    }
}

// MARK: - 数据库大小信息模型

struct DatabaseSizeInfo: Codable {
    let totalFileSizeBytes: Int
    let tableStats: [TableSizeInfo]
    let lastUpdated: Date

    var formattedTotalSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(totalFileSizeBytes), countStyle: .file)
    }

    var totalRecords: Int {
        tableStats.reduce(0) { $0 + $1.recordCount }
    }
}

struct TableSizeInfo: Codable {
    let tableName: String
    let recordCount: Int
    let estimatedSizeBytes: Int

    var formattedSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(estimatedSizeBytes), countStyle: .file)
    }
}

// MARK: - SQLite辅助方法扩展

extension OptimizedSystemMonitorDatabase {
    /// 从SQLite列获取可选的Double值
    /// - Parameters:
    ///   - statement: SQLite预编译语句
    ///   - columnIndex: 列索引
    ///   - scale: 缩放因子，用于百分比转换等
    /// - Returns: 可选的Double值，如果列为NULL则返回nil
    private func getOptionalDoubleFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32, scale: Double = 1.0) -> Double? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return sqlite3_column_double(statement, columnIndex) / scale
    }

    /// 从SQLite列获取可选的Int值
    /// - Parameters:
    ///   - statement: SQLite预编译语句
    ///   - columnIndex: 列索引
    /// - Returns: 可选的Int值，如果列为NULL则返回nil
    private func getOptionalIntFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Int? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return Int(sqlite3_column_int(statement, columnIndex))
    }

    /// 从SQLite列获取可选的Int64值
    /// - Parameters:
    ///   - statement: SQLite预编译语句
    ///   - columnIndex: 列索引
    /// - Returns: 可选的Int64值，如果列为NULL则返回nil
    private func getOptionalInt64FromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Int64? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return sqlite3_column_int64(statement, columnIndex)
    }

    /// 从SQLite列获取可选的String值
    /// - Parameters:
    ///   - statement: SQLite预编译语句
    ///   - columnIndex: 列索引
    /// - Returns: 可选的String值，如果列为NULL则返回nil
    private func getOptionalStringFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> String? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL,
              let cString = sqlite3_column_text(statement, columnIndex) else { return nil }
        return String(cString: cString)
    }

    /// 从SQLite列获取可选的Date值
    /// - Parameters:
    ///   - statement: SQLite预编译语句
    ///   - columnIndex: 列索引
    /// - Returns: 可选的Date值，如果列为NULL则返回nil
    private func getOptionalDateFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Date? {
        guard let timestamp = getOptionalInt64FromColumn(statement, columnIndex) else { return nil }
        return Date(timeIntervalSince1970: TimeInterval(timestamp))
    }

    /// 从SQLite列获取可选的Bool值
    /// - Parameters:
    ///   - statement: SQLite预编译语句
    ///   - columnIndex: 列索引
    /// - Returns: 可选的Bool值，如果列为NULL则返回nil
    private func getOptionalBoolFromColumn(_ statement: OpaquePointer, _ columnIndex: Int32) -> Bool? {
        guard sqlite3_column_type(statement, columnIndex) != SQLITE_NULL else { return nil }
        return sqlite3_column_int(statement, columnIndex) != 0
    }
}
