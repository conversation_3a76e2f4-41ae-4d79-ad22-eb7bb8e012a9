//
//  OptimizedSystemMonitorDatabase+SystemInfo.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  系统信息数据库操作扩展
//

import Foundation
import SQLite3

// MARK: - 系统信息操作

/**
 * 系统信息数据库操作扩展
 * 
 * 提供系统基本信息的存储和查询功能，包括：
 * - 设备型号和硬件配置
 * - CPU信息（型号、核心数、架构）
 * - 内存和存储容量
 * - 操作系统版本信息
 * - 系统序列号等标识信息
 */
extension OptimizedSystemMonitorDatabase {
    
    /**
     * 插入或更新系统信息
     * 
     * 使用INSERT OR REPLACE语句确保系统信息的唯一性，
     * 如果记录已存在则更新，否则插入新记录。
     * 
     * 数据转换说明：
     * - 内存容量从GB转换为MB存储（提高精度）
     * - 时间戳转换为Unix时间戳格式
     * - 字符串使用UTF-8编码存储
     * 
     * @param systemInfo 系统信息对象
     * @return 操作结果，成功返回.success(())，失败返回DatabaseError
     */
    func insertOrUpdateSystemInfo(_ systemInfo: SystemInfo) -> Result<Void, DatabaseError> {
        return executeInTransaction {
            let sql = """
                INSERT OR REPLACE INTO system_info
                (device_model, cpu_name, cpu_core_count, cpu_architecture, total_memory_mb,
                 total_disk_gb, macos_version, kernel_version, system_serial, recorded_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_system_info") else {
                throw DatabaseError.queryError("准备系统信息插入语句失败")
            }

            // SQLite字符串析构函数，确保字符串在使用后正确释放
            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
            
            // 绑定参数到预编译语句
            sqlite3_bind_text(statement, 1, (systemInfo.deviceModel as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 2, (systemInfo.cpuName as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(statement, 3, Int32(systemInfo.cpuCoreCount))
            sqlite3_bind_text(statement, 4, (systemInfo.cpuArchitecture as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(statement, 5, Int32(systemInfo.totalMemoryGB * 1024))  // GB转MB
            sqlite3_bind_int(statement, 6, Int32(systemInfo.totalDiskGB))
            sqlite3_bind_text(statement, 7, (systemInfo.macOSVersion as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 8, (systemInfo.kernelVersion as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 9, (systemInfo.systemSerial as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int64(statement, 10, Int64(systemInfo.recordedAt.timeIntervalSince1970))

            // 执行SQL语句
            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入系统信息失败: \(errorMessage)")
            }

            Logger.info("系统信息插入成功", category: "Database")
        }
    }
    
    /**
     * 获取最新的系统信息
     * 
     * 从数据库中查询最新记录的系统信息，按记录时间降序排列。
     * 
     * 数据转换说明：
     * - 内存容量从MB转换回GB显示
     * - Unix时间戳转换为Date对象
     * - 处理可能的NULL值
     * 
     * @return 操作结果，成功返回SystemInfo对象（可能为nil），失败返回DatabaseError
     */
    func getSystemInfo() -> Result<SystemInfo?, DatabaseError> {
        return safeDBSync {
            let sql = "SELECT * FROM system_info ORDER BY recorded_at DESC LIMIT 1;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备系统信息查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            if sqlite3_step(statement) == SQLITE_ROW {
                // 从数据库列中提取数据
                let deviceModel = String(cString: sqlite3_column_text(statement, 1))
                let cpuName = String(cString: sqlite3_column_text(statement, 2))
                let cpuCoreCount = Int(sqlite3_column_int(statement, 3))
                let cpuArchitecture = String(cString: sqlite3_column_text(statement, 4))
                let totalMemoryMB = sqlite3_column_int(statement, 5)
                let totalDiskGB = sqlite3_column_int(statement, 6)
                let macOSVersion = String(cString: sqlite3_column_text(statement, 7))
                let kernelVersion = String(cString: sqlite3_column_text(statement, 8))
                let systemSerial = String(cString: sqlite3_column_text(statement, 9))
                let recordedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 10)))

                // 构造SystemInfo对象
                let systemInfo = SystemInfo(
                    deviceModel: deviceModel,
                    cpuName: cpuName,
                    cpuCoreCount: cpuCoreCount,
                    cpuArchitecture: cpuArchitecture,
                    totalMemoryGB: Double(totalMemoryMB) / 1024.0, // MB转换回GB
                    totalDiskGB: Double(totalDiskGB),
                    macOSVersion: macOSVersion,
                    kernelVersion: kernelVersion,
                    systemSerial: systemSerial,
                    recordedAt: recordedAt
                )

                return .success(systemInfo)
            }

            // 没有找到记录
            return .success(nil)
        }
    }
    
    /**
     * 获取系统信息历史记录
     * 
     * 查询指定时间范围内的系统信息变更历史，
     * 用于跟踪系统配置的变化。
     * 
     * @param from 开始时间
     * @param to 结束时间
     * @param limit 最大返回记录数（默认100）
     * @return 操作结果，成功返回SystemInfo数组，失败返回DatabaseError
     */
    func getSystemInfoHistory(from startDate: Date, to endDate: Date, limit: Int = 100) -> Result<[SystemInfo], DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT * FROM system_info 
                WHERE recorded_at BETWEEN ? AND ? 
                ORDER BY recorded_at DESC 
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备系统信息历史查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            // 绑定参数
            sqlite3_bind_int64(statement, 1, Int64(startDate.timeIntervalSince1970))
            sqlite3_bind_int64(statement, 2, Int64(endDate.timeIntervalSince1970))
            sqlite3_bind_int(statement, 3, Int32(limit))

            var systemInfoList: [SystemInfo] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let deviceModel = String(cString: sqlite3_column_text(statement, 1))
                let cpuName = String(cString: sqlite3_column_text(statement, 2))
                let cpuCoreCount = Int(sqlite3_column_int(statement, 3))
                let cpuArchitecture = String(cString: sqlite3_column_text(statement, 4))
                let totalMemoryMB = sqlite3_column_int(statement, 5)
                let totalDiskGB = sqlite3_column_int(statement, 6)
                let macOSVersion = String(cString: sqlite3_column_text(statement, 7))
                let kernelVersion = String(cString: sqlite3_column_text(statement, 8))
                let systemSerial = String(cString: sqlite3_column_text(statement, 9))
                let recordedAt = Date(timeIntervalSince1970: TimeInterval(sqlite3_column_int64(statement, 10)))

                let systemInfo = SystemInfo(
                    deviceModel: deviceModel,
                    cpuName: cpuName,
                    cpuCoreCount: cpuCoreCount,
                    cpuArchitecture: cpuArchitecture,
                    totalMemoryGB: Double(totalMemoryMB) / 1024.0,
                    totalDiskGB: Double(totalDiskGB),
                    macOSVersion: macOSVersion,
                    kernelVersion: kernelVersion,
                    systemSerial: systemSerial,
                    recordedAt: recordedAt
                )

                systemInfoList.append(systemInfo)
            }

            return .success(systemInfoList)
        }
    }
    
    /**
     * 检查系统信息是否存在
     * 
     * 快速检查数据库中是否已存在系统信息记录。
     * 
     * @return 操作结果，成功返回布尔值，失败返回DatabaseError
     */
    func hasSystemInfo() -> Result<Bool, DatabaseError> {
        return safeDBSync {
            let sql = "SELECT COUNT(*) FROM system_info LIMIT 1;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备系统信息存在性检查语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            if sqlite3_step(statement) == SQLITE_ROW {
                let count = sqlite3_column_int(statement, 0)
                return .success(count > 0)
            }

            return .success(false)
        }
    }
    
    /**
     * 删除过期的系统信息记录
     * 
     * 删除指定天数之前的系统信息记录，保持数据库整洁。
     * 通常系统信息变化不频繁，可以保留较长时间的历史记录。
     * 
     * @param olderThanDays 保留天数
     * @return 操作结果，成功返回删除的记录数，失败返回DatabaseError
     */
    func cleanupOldSystemInfo(olderThanDays days: Int) -> Result<Int, DatabaseError> {
        return executeInTransaction {
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
            let cutoffTimestamp = Int64(cutoffDate.timeIntervalSince1970)

            let sql = "DELETE FROM system_info WHERE recorded_at < ?;"

            guard let statement = prepareStatement(sql) else {
                throw DatabaseError.queryError("准备系统信息清理语句失败")
            }

            sqlite3_bind_int64(statement, 1, cutoffTimestamp)

            if sqlite3_step(statement) == SQLITE_DONE {
                let deletedCount = sqlite3_changes(db)
                Logger.info("清理系统信息: 删除\(deletedCount)条记录", category: "Database")
                return Int(deletedCount)
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("清理系统信息失败: \(errorMessage)")
            }
        }
    }
}
