//
//  OptimizedSystemMonitorDatabase+Maintenance.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库维护和清理操作扩展
//

import Foundation
import SQLite3

// MARK: - 数据清理和维护

/**
 * 数据库维护和清理操作扩展
 * 
 * 提供数据库的日常维护功能，包括：
 * - 过期数据清理
 * - 数据库优化和压缩
 * - 统计信息更新
 * - 健康状态检查
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 清理指定天数之前的数据
     * 
     * 删除超过指定天数的历史数据，以控制数据库大小和提高性能。
     * 
     * 清理策略：
     * - 基于timestamp字段进行时间过滤
     * - 分表清理，避免长时间锁定
     * - 事务保护，确保操作原子性
     * - 详细的清理统计和日志记录
     * 
     * 清理范围：
     * - 各类性能指标数据（CPU、内存、磁盘等）
     * - 进程快照数据
     * - 应用使用详细记录
     * - 应用使用会话（单独处理）
     * 
     * @param days 保留天数，超过此天数的数据将被删除
     * @return 操作结果，成功返回删除的记录总数，失败返回DatabaseError
     */
    func cleanupOldData(olderThanDays days: Int) -> Result<Int, DatabaseError> {
        return executeInTransaction {
            // 计算截止时间
            let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
            let cutoffTimestamp = Int64(cutoffDate.timeIntervalSince1970)

            // 需要清理的指标数据表
            let tables = [
                "cpu_metrics",
                "memory_metrics",
                "disk_metrics",
                "battery_metrics",
                "thermal_metrics",
                "network_metrics",
                "process_snapshots",
                "app_usage_details"
            ]

            var totalDeleted = 0

            // 逐表清理过期数据
            for table in tables {
                let sql = "DELETE FROM \(table) WHERE timestamp < ?;"

                guard let statement = prepareStatement(sql) else {
                    throw DatabaseError.queryError("准备清理\(table)表语句失败")
                }

                sqlite3_bind_int64(statement, 1, cutoffTimestamp)

                if sqlite3_step(statement) == SQLITE_DONE {
                    let deletedCount = sqlite3_changes(db)
                    totalDeleted += Int(deletedCount)
                    Logger.info("清理\(table)表: 删除\(deletedCount)条记录", category: "Database")
                } else {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("清理\(table)表失败: \(errorMessage)")
                }

                sqlite3_finalize(statement)
            }

            // 单独处理应用使用会话表（可能有不同的清理策略）
            let sessionSQL = "DELETE FROM app_usage_sessions WHERE session_start < ?;"
            guard let sessionStatement = prepareStatement(sessionSQL) else {
                throw DatabaseError.queryError("准备清理会话表语句失败")
            }

            sqlite3_bind_int64(sessionStatement, 1, cutoffTimestamp)

            if sqlite3_step(sessionStatement) == SQLITE_DONE {
                let deletedCount = sqlite3_changes(db)
                totalDeleted += Int(deletedCount)
                Logger.info("清理app_usage_sessions表: 删除\(deletedCount)条记录", category: "Database")
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("清理会话表失败: \(errorMessage)")
            }

            sqlite3_finalize(sessionStatement)

            Logger.info("数据清理完成，总共删除\(totalDeleted)条记录", category: "Database")
            return totalDeleted
        }
    }

    /**
     * 清空所有数据
     * 
     * 删除所有表中的数据，但保留表结构。
     * 这是一个危险操作，通常用于重置或测试。
     * 
     * @return 操作结果，成功返回true，失败返回DatabaseError
     */
    func clearAllData() -> Result<Bool, DatabaseError> {
        return executeInTransaction {
            let tables = [
                "system_info",
                "app_info",
                "cpu_metrics",
                "memory_metrics",
                "disk_metrics",
                "battery_metrics",
                "thermal_metrics",
                "network_metrics",
                "process_snapshots",
                "app_usage_sessions",
                "app_usage_details"
            ]

            for table in tables {
                let sql = "DELETE FROM \(table);"

                guard let statement = prepareStatement(sql) else {
                    throw DatabaseError.queryError("准备清空\(table)表语句失败")
                }

                if sqlite3_step(statement) != SQLITE_DONE {
                    let errorMessage = String(cString: sqlite3_errmsg(db))
                    throw DatabaseError.queryError("清空\(table)表失败: \(errorMessage)")
                }

                sqlite3_finalize(statement)
                Logger.info("已清空\(table)表", category: "Database")
            }

            Logger.warning("所有数据已清空", category: "Database")
            return true
        }
    }

    /**
     * 压缩数据库
     * 
     * 执行VACUUM操作来压缩数据库文件，回收未使用的空间。
     * 这个操作可能需要较长时间，建议在低峰期执行。
     * 
     * @return 操作结果，成功返回压缩前后的大小信息，失败返回DatabaseError
     */
    func vacuumDatabase() -> Result<String, DatabaseError> {
        return safeDBSync {
            // 记录压缩前的大小
            let sizeBefore = getDatabaseSize()

            // 执行VACUUM操作
            if !executeSQL("VACUUM;", description: "数据库压缩") {
                return .failure(.operationError("数据库压缩失败"))
            }

            // 记录压缩后的大小
            let sizeAfter = getDatabaseSize()
            let savedBytes = sizeBefore - sizeAfter
            let savedMB = Double(savedBytes) / (1024 * 1024)

            let result = String(format: "数据库压缩完成 - 压缩前: %.1fMB, 压缩后: %.1fMB, 节省: %.1fMB",
                               Double(sizeBefore) / (1024 * 1024),
                               Double(sizeAfter) / (1024 * 1024),
                               savedMB)

            Logger.info(result, category: "Database")
            return .success(result)
        }
    }

    /**
     * 分析数据库
     * 
     * 执行ANALYZE操作来更新查询优化器的统计信息。
     * 这有助于提高查询性能。
     * 
     * @return 操作结果，成功返回true，失败返回DatabaseError
     */
    func analyzeDatabase() -> Result<Bool, DatabaseError> {
        return safeDBSync {
            if executeSQL("ANALYZE;", description: "数据库分析") {
                Logger.info("数据库分析完成", category: "Database")
                return .success(true)
            } else {
                return .failure(.operationError("数据库分析失败"))
            }
        }
    }

    /**
     * 重建索引
     * 
     * 重建所有索引以优化查询性能。
     * 
     * @return 操作结果，成功返回重建的索引数量，失败返回DatabaseError
     */
    func rebuildIndexes() -> Result<Int, DatabaseError> {
        return safeDBSync {
            // 获取所有索引
            let sql = "SELECT name FROM sqlite_master WHERE type='index' AND sql IS NOT NULL;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备索引查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var indexNames: [String] = []
            while sqlite3_step(statement) == SQLITE_ROW {
                let indexName = String(cString: sqlite3_column_text(statement, 0))
                indexNames.append(indexName)
            }

            // 重建每个索引
            var rebuiltCount = 0
            for indexName in indexNames {
                let reindexSQL = "REINDEX \(indexName);"
                if executeSQL(reindexSQL, description: "重建索引: \(indexName)") {
                    rebuiltCount += 1
                }
            }

            Logger.info("重建索引完成，共重建\(rebuiltCount)个索引", category: "Database")
            return .success(rebuiltCount)
        }
    }

    /**
     * 获取数据库统计信息
     * 
     * 收集数据库的详细统计信息，包括各表的记录数、大小等。
     * 
     * @return 操作结果，成功返回DatabaseStats对象，失败返回DatabaseError
     */
    func getDatabaseStats() -> Result<DatabaseStats, DatabaseError> {
        return safeDBSync {
            var stats = DatabaseStats()
            stats.databaseSizeBytes = getDatabaseSize()

            // 获取各表的记录数
            let tables = [
                "system_info", "app_info", "cpu_metrics", "memory_metrics",
                "disk_metrics", "battery_metrics", "thermal_metrics",
                "network_metrics", "process_snapshots", "app_usage_sessions",
                "app_usage_details"
            ]

            for table in tables {
                let sql = "SELECT COUNT(*) FROM \(table);"

                guard let statement = prepareStatement(sql) else {
                    continue
                }

                if sqlite3_step(statement) == SQLITE_ROW {
                    let count = Int(sqlite3_column_int(statement, 0))
                    stats.tableCounts[table] = count
                }

                sqlite3_finalize(statement)
            }

            // 获取数据时间跨度
            let timeRangeSQL = """
                SELECT 
                    MIN(timestamp) as earliest,
                    MAX(timestamp) as latest
                FROM (
                    SELECT timestamp FROM cpu_metrics
                    UNION ALL
                    SELECT timestamp FROM memory_metrics
                    UNION ALL
                    SELECT timestamp FROM disk_metrics
                ) AS all_timestamps;
            """

            guard let timeStatement = prepareStatement(timeRangeSQL) else {
                return .success(stats)
            }

            if sqlite3_step(timeStatement) == SQLITE_ROW {
                if sqlite3_column_type(timeStatement, 0) != SQLITE_NULL {
                    let earliest = sqlite3_column_int64(timeStatement, 0)
                    stats.earliestDataTime = Date(timeIntervalSince1970: TimeInterval(earliest))
                }

                if sqlite3_column_type(timeStatement, 1) != SQLITE_NULL {
                    let latest = sqlite3_column_int64(timeStatement, 1)
                    stats.latestDataTime = Date(timeIntervalSince1970: TimeInterval(latest))
                }
            }

            sqlite3_finalize(timeStatement)

            return .success(stats)
        }
    }
}
