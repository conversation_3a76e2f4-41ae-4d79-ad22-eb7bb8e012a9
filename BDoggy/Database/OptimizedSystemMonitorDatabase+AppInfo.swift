//
//  OptimizedSystemMonitorDatabase+AppInfo.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  应用信息数据库操作扩展
//

import Foundation
import SQLite3

// MARK: - 应用信息操作

/**
 * 应用信息数据库操作扩展
 * 
 * 提供应用程序信息的存储、查询和管理功能，包括：
 * - 应用基本信息（Bundle ID、名称、版本）
 * - 应用分类和厂商信息
 * - 系统应用标识
 * - 安装和更新时间记录
 */
extension OptimizedSystemMonitorDatabase {

    /**
     * 插入或更新应用信息
     * 
     * 使用INSERT OR REPLACE语句确保应用信息的唯一性，
     * 支持可选参数，允许部分信息的更新。
     * 
     * 参数处理：
     * - bundleID: 应用的唯一标识符（必需）
     * - appName: 应用显示名称（必需）
     * - appVersion: 应用版本号（可选，NULL值处理）
     * - appCategory: 应用分类（可选，NULL值处理）
     * - isSystemApp: 是否为系统应用（布尔值转整数存储）
     * 
     * @param bundleID 应用Bundle标识符
     * @param appName 应用名称
     * @param appVersion 应用版本（可选）
     * @param appCategory 应用分类（可选）
     * @param isSystemApp 是否为系统应用
     * @return 操作结果，成功返回插入记录的ID，失败返回DatabaseError
     */
    func insertOrUpdateAppInfo(bundleID: String, appName: String, appVersion: String? = nil,
                               appCategory: String? = nil, isSystemApp: Bool = false) -> Result<Int64, DatabaseError>
    {
        return executeInTransaction {
            let sql = """
                INSERT OR REPLACE INTO app_info
                (bundle_id, app_name, app_version, app_category, is_system_app)
                VALUES (?, ?, ?, ?, ?);
            """

            guard let statement = prepareStatement(sql, cacheKey: "insert_app_info") else {
                throw DatabaseError.queryError("准备应用信息插入语句失败")
            }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)

            // 绑定必需参数
            sqlite3_bind_text(statement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 2, (appName as NSString).utf8String, -1, SQLITE_TRANSIENT)

            // 绑定可选参数，处理NULL值
            if let version = appVersion {
                sqlite3_bind_text(statement, 3, (version as NSString).utf8String, -1, SQLITE_TRANSIENT)
            } else {
                sqlite3_bind_null(statement, 3)
            }

            if let category = appCategory {
                sqlite3_bind_text(statement, 4, (category as NSString).utf8String, -1, SQLITE_TRANSIENT)
            } else {
                sqlite3_bind_null(statement, 4)
            }

            // 布尔值转换为整数存储
            sqlite3_bind_int(statement, 5, isSystemApp ? 1 : 0)

            // 执行插入操作
            if sqlite3_step(statement) != SQLITE_DONE {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("插入应用信息失败: \(errorMessage)")
            }

            // 返回新插入记录的ID
            return sqlite3_last_insert_rowid(db)
        }
    }

    /**
     * 根据Bundle ID获取应用信息
     * 
     * 通过应用的唯一标识符查询应用详细信息。
     * 
     * @param bundleID 应用Bundle标识符
     * @return 操作结果，成功返回AppInfo对象（可能为nil），失败返回DatabaseError
     */
    func getAppInfo(bundleID: String) -> Result<AppInfo?, DatabaseError> {
        return safeDBSync {
            let sql = "SELECT * FROM app_info WHERE bundle_id = ? LIMIT 1;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用信息查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)

            sqlite3_bind_text(statement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)

            if sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let appName = String(cString: sqlite3_column_text(statement, 2))

                // 处理可选的应用版本
                let appVersion: String? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 3))
                    }
                    return nil
                }()

                // 处理可选的应用分类
                let appCategory: String? = {
                    if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 4))
                    }
                    return nil
                }()

                let isSystemApp = sqlite3_column_int(statement, 7) == 1

                let appInfo = AppInfo(
                    id: id,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: appVersion,
                    appCategory: appCategory,
                    isSystemApp: isSystemApp
                )

                return .success(appInfo)
            }

            return .success(nil)
        }
    }

    /**
     * 获取所有应用信息
     * 
     * 查询数据库中的所有应用信息，支持过滤系统应用。
     * 
     * @param includeSystemApps 是否包含系统应用（默认false）
     * @return 操作结果，成功返回AppInfo数组，失败返回DatabaseError
     */
    func getAllApps(includeSystemApps: Bool = false) -> Result<[AppInfo], DatabaseError> {
        return safeDBSync {
            var sql = "SELECT * FROM app_info"
            if !includeSystemApps {
                sql += " WHERE is_system_app = 0"
            }
            sql += " ORDER BY app_name;"

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用列表查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            var apps: [AppInfo] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let appName = String(cString: sqlite3_column_text(statement, 2))

                let appVersion: String? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 3))
                    }
                    return nil
                }()

                let appCategory: String? = {
                    if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 4))
                    }
                    return nil
                }()

                let isSystemApp = sqlite3_column_int(statement, 7) == 1

                let appInfo = AppInfo(
                    id: id,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: appVersion,
                    appCategory: appCategory,
                    isSystemApp: isSystemApp
                )

                apps.append(appInfo)
            }

            return .success(apps)
        }
    }

    /**
     * 获取或创建应用信息
     *
     * 如果应用信息已存在则返回现有记录，否则创建新记录。
     * 这是一个便利方法，常用于应用使用会话记录时。
     *
     * @param bundleID 应用Bundle标识符
     * @param appName 应用名称
     * @return 操作结果，成功返回AppInfo对象，失败返回DatabaseError
     */
    func getOrCreateAppInfo(bundleID: String, appName: String) -> Result<AppInfo, DatabaseError> {
        return safeDBSync {
            // 首先尝试获取现有的应用信息
            switch getAppInfo(bundleID: bundleID) {
            case .success(let existingApp):
                if let app = existingApp {
                    // 应用信息已存在，返回现有记录
                    return .success(app)
                }
                // 应用信息不存在，创建新记录
                break
            case .failure(let error):
                return .failure(error)
            }

            // 创建新的应用信息记录
            switch insertOrUpdateAppInfo(bundleID: bundleID, appName: appName) {
            case .success(let newID):
                // 创建成功，构造并返回AppInfo对象
                let newApp = AppInfo(
                    id: newID,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: nil,
                    appCategory: nil,
                    isSystemApp: false
                )
                return .success(newApp)
            case .failure(let error):
                return .failure(error)
            }
        }
    }

    /**
     * 根据应用名称搜索应用
     *
     * 支持模糊搜索应用名称，用于应用查找和过滤。
     *
     * @param searchTerm 搜索关键词
     * @param limit 最大返回数量（默认50）
     * @return 操作结果，成功返回AppInfo数组，失败返回DatabaseError
     */
    func searchApps(by searchTerm: String, limit: Int = 50) -> Result<[AppInfo], DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT * FROM app_info
                WHERE app_name LIKE ? OR bundle_id LIKE ?
                ORDER BY app_name
                LIMIT ?;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用搜索语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
            let searchPattern = "%\(searchTerm)%"

            sqlite3_bind_text(statement, 1, (searchPattern as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_text(statement, 2, (searchPattern as NSString).utf8String, -1, SQLITE_TRANSIENT)
            sqlite3_bind_int(statement, 3, Int32(limit))

            var apps: [AppInfo] = []

            while sqlite3_step(statement) == SQLITE_ROW {
                let id = sqlite3_column_int64(statement, 0)
                let bundleID = String(cString: sqlite3_column_text(statement, 1))
                let appName = String(cString: sqlite3_column_text(statement, 2))

                let appVersion: String? = {
                    if sqlite3_column_type(statement, 3) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 3))
                    }
                    return nil
                }()

                let appCategory: String? = {
                    if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                        return String(cString: sqlite3_column_text(statement, 4))
                    }
                    return nil
                }()

                let isSystemApp = sqlite3_column_int(statement, 7) == 1

                let appInfo = AppInfo(
                    id: id,
                    bundleID: bundleID,
                    appName: appName,
                    appVersion: appVersion,
                    appCategory: appCategory,
                    isSystemApp: isSystemApp
                )

                apps.append(appInfo)
            }

            return .success(apps)
        }
    }

    /**
     * 获取应用数量统计
     *
     * 返回数据库中应用的数量统计信息。
     *
     * @return 操作结果，成功返回统计信息字典，失败返回DatabaseError
     */
    func getAppCountStats() -> Result<[String: Int], DatabaseError> {
        return safeDBSync {
            let sql = """
                SELECT
                    COUNT(*) as total_apps,
                    COUNT(CASE WHEN is_system_app = 1 THEN 1 END) as system_apps,
                    COUNT(CASE WHEN is_system_app = 0 THEN 1 END) as user_apps
                FROM app_info;
            """

            guard let statement = prepareStatement(sql) else {
                return .failure(.queryError("准备应用统计查询语句失败"))
            }

            defer { sqlite3_finalize(statement) }

            if sqlite3_step(statement) == SQLITE_ROW {
                let totalApps = Int(sqlite3_column_int(statement, 0))
                let systemApps = Int(sqlite3_column_int(statement, 1))
                let userApps = Int(sqlite3_column_int(statement, 2))

                let stats = [
                    "total_apps": totalApps,
                    "system_apps": systemApps,
                    "user_apps": userApps
                ]

                return .success(stats)
            }

            return .success([:])
        }
    }

    /**
     * 删除应用信息
     *
     * 根据Bundle ID删除应用信息记录。
     * 注意：这会级联删除相关的使用会话数据。
     *
     * @param bundleID 应用Bundle标识符
     * @return 操作结果，成功返回true，失败返回DatabaseError
     */
    func deleteAppInfo(bundleID: String) -> Result<Bool, DatabaseError> {
        return executeInTransaction {
            let sql = "DELETE FROM app_info WHERE bundle_id = ?;"

            guard let statement = prepareStatement(sql) else {
                throw DatabaseError.queryError("准备删除应用信息语句失败")
            }

            let SQLITE_TRANSIENT = unsafeBitCast(-1, to: sqlite3_destructor_type.self)
            sqlite3_bind_text(statement, 1, (bundleID as NSString).utf8String, -1, SQLITE_TRANSIENT)

            if sqlite3_step(statement) == SQLITE_DONE {
                let deletedCount = sqlite3_changes(db)
                Logger.info("删除应用信息: \(bundleID), 影响行数: \(deletedCount)", category: "Database")
                return deletedCount > 0
            } else {
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw DatabaseError.queryError("删除应用信息失败: \(errorMessage)")
            }
        }
    }
}
