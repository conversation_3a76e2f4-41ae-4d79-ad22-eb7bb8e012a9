//
//  SystemMonitorInitializer.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import AppKit
import Foundation

/// 系统监控初始化器
/// 负责初始化和配置整个系统监控模块
class SystemMonitorInitializer {
    static let shared = SystemMonitorInitializer()
    
    private var isInitialized = false
    
    private init() {}
    
    /// 初始化系统监控模块
    func initialize() {
        guard !isInitialized else {
            Logger.warning("系统监控模块已经初始化")
            return
        }
        
        Logger.info("开始初始化系统监控模块...")
        
        // 1. 设置日志系统
        
        // 2. 检查权限
        checkPermissions()
        
        // 3. 初始化数据库
        initializeDatabase()
        
        // 4. 配置监控调度器
        configureScheduler()
        
        // 5. 设置应用生命周期监听
        setupAppLifecycleObservers()
        
        isInitialized = true
        Logger.success("系统监控模块初始化完成")
    }
    
    /// 启动系统监控
    func startMonitoring() {
        guard isInitialized else {
            Logger.error("系统监控模块未初始化，请先调用initialize()")
            return
        }
        
        let permissionManager = PermissionManager.shared
        
        // 检查必要权限
//        if !permissionManager.hasRequiredPermissionsForMonitoring() {
//            Logger.warning("缺少必要权限，无法启动系统监控")
//            return
//        }
        let scheduler = SystemMonitorScheduler.shared
        scheduler.startMonitoring()
        Logger.info("系统监控已启动")
    }
    
    /// 停止系统监控
    func stopMonitoring() {
        let scheduler = SystemMonitorScheduler.shared
        scheduler.stopMonitoring()
        Logger.info("系统监控已停止")
    }
    
    // MARK: - 私有方法
    
    /// 检查权限
    private func checkPermissions() {
        Logger.info("开始检查系统权限...")

        let permissionManager = PermissionManager.shared
        permissionManager.checkAllPermissions()

        let permissions = permissionManager.getAllPermissionStatus()
        Logger.info("权限检查完成: \(permissions)")

        // 检查必要权限
        let hasRequiredPermissions = permissionManager.hasRequiredPermissionsForMonitoring()
        if hasRequiredPermissions {
            Logger.success("所有必要权限已获得")
        } else {
            Logger.warning("缺少必要权限，某些功能可能无法正常工作")

            // 详细记录缺少的权限
            let missingPermissions = permissionManager.getMissingPermissions()
            if !missingPermissions.isEmpty {
                let missingNames = missingPermissions.map { $0.displayName }.joined(separator: ", ")
                Logger.warning("缺少权限: \(missingNames)")

                // 提供权限获取建议
                Logger.info("建议：请在系统偏好设置 > 安全性与隐私 > 隐私 中授予相应权限")
            }
        }
    }
    
    /// 初始化数据库
    private func initializeDatabase() {
        Logger.info("开始初始化数据库...")

        let database = OptimizedSystemMonitorDatabase.shared

        // 检查数据库健康状态
        let isHealthy = database.checkDatabaseHealth()
        if !isHealthy {
            Logger.error("数据库健康检查失败，尝试修复...")
            // 这里可以添加数据库修复逻辑
        } else {
            Logger.success("数据库健康检查通过")
        }

        // 记录系统信息（如果还没有记录）
        let systemInfoResult = database.getSystemInfo()

        switch systemInfoResult {
        case .success(let existingSystemInfo):
            if let systemInfo = existingSystemInfo {
                Logger.info("系统信息已存在，设备型号: \(systemInfo.deviceModel)")
            } else {
                // 首次运行，创建系统信息记录
                Logger.info("首次运行，创建系统信息记录...")
                let newSystemInfo = SystemInfo()
                let insertResult = database.insertOrUpdateSystemInfo(newSystemInfo)

                switch insertResult {
                case .success(_):
                    Logger.success("系统信息记录创建成功")
                case .failure(let error):
                    Logger.error("创建系统信息记录失败: \(error)")
                }
            }
        case .failure(let error):
            Logger.error("获取系统信息失败: \(error)")
        }

        Logger.success("数据库初始化完成")
    }
    
    /// 配置监控调度器
    private func configureScheduler() {
        Logger.info("开始配置监控调度器...")

        let scheduler = SystemMonitorScheduler.shared

        // 初始化调度器
        scheduler.initialize()

        // 验证配置
        let config = scheduler.config
        Logger.info("监控调度器配置完成")
        Logger.debug("采集间隔: \(config.collectionInterval)秒")
        Logger.debug("数据保留天数: \(config.maxDataRetentionDays)天")
        Logger.debug("应用使用跟踪: \(config.enableAppUsageTracking ? "启用" : "禁用")")

        // 验证配置合理性
        if config.collectionInterval < 1.0 {
            Logger.warning("采集间隔过短，可能影响系统性能")
        }

        if config.maxDataRetentionDays < 1 {
            Logger.warning("数据保留天数过短，历史数据可能丢失")
        }

        Logger.success("监控调度器配置验证完成")
    }
    
    /// 设置应用生命周期监听
    private func setupAppLifecycleObservers() {
        // 应用启动时自动开始监控（如果有权限）
        NotificationCenter.default.addObserver(
            forName: NSApplication.didFinishLaunchingNotification,
            object: nil,
            queue: .main
        ) { _ in
            // 延迟启动，确保应用完全加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.autoStartMonitoringIfPossible()
            }
        }
        
        // 应用即将退出时停止监控
        NotificationCenter.default.addObserver(
            forName: NSApplication.willTerminateNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.stopMonitoring()
        }
        
        // 系统睡眠时暂停监控
        NSWorkspace.shared.notificationCenter.addObserver(
            forName: NSWorkspace.willSleepNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.info("系统即将睡眠，暂停监控")
            self.stopMonitoring()
        }
        
        // 系统唤醒时恢复监控
        NSWorkspace.shared.notificationCenter.addObserver(
            forName: NSWorkspace.didWakeNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.info("系统已唤醒，恢复监控")
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.autoStartMonitoringIfPossible()
            }
        }
        
        Logger.info("应用生命周期监听器设置完成")
    }
    
    /// 如果可能的话自动启动监控
    private func autoStartMonitoringIfPossible() {
        let permissionManager = PermissionManager.shared
        let scheduler = SystemMonitorScheduler.shared
        
        // 检查是否已经在运行
        if scheduler.isMonitoring {
            return
        }
        
        // 检查是否有必要权限
        if permissionManager.hasRequiredPermissionsForMonitoring() {
            startMonitoring()
        } else {
            Logger.info("缺少必要权限，跳过自动启动监控")
        }
    }
    
    // MARK: - 公共工具方法
    
    /// 获取系统监控状态
    func getMonitoringStatus() -> [String: Any] {
        let scheduler = SystemMonitorScheduler.shared
        let permissionManager = PermissionManager.shared
        let database = OptimizedSystemMonitorDatabase.shared
        
        return [
            "is_initialized": isInitialized,
            "is_monitoring": scheduler.isMonitoring,
            "last_collection_time": scheduler.lastCollectionTime?.timeIntervalSince1970 ?? 0,
            "total_data_points": scheduler.totalDataPoints,
            "database_size_bytes": database.getDatabaseSize(),
            "permissions": permissionManager.getAllPermissionStatus(),
            "config": [
                "collection_interval": scheduler.config.collectionInterval,
                "max_retention_days": scheduler.config.maxDataRetentionDays,
                "enable_battery_monitoring": scheduler.config.enableBatteryMonitoring,
                "enable_temperature_monitoring": scheduler.config.enableTemperatureMonitoring,
                "enable_app_usage_tracking": scheduler.config.enableAppUsageTracking
            ]
        ]
    }
    
    /// 执行数据清理
    func performMaintenance() {
        Logger.info("开始执行系统监控维护任务")
        
        let scheduler = SystemMonitorScheduler.shared
        let database = OptimizedSystemMonitorDatabase.shared
        
        // 清理旧数据
        let success = scheduler.cleanupOldData()
        if success {
            Logger.info("旧数据清理完成")
        } else {
            Logger.error("旧数据清理失败")
        }
        
        // 记录数据库状态
        let dbSize = database.getDatabaseSize()
        Logger.info("维护后数据库大小: \(formatFileSize(dbSize))")
        
        // 清理日志文件（如果太大）
        let logSize = Logger.getLogFileSize()
        if logSize > 10 * 1024 * 1024 { // 10MB
            Logger.clearLogFile()
            Logger.info("日志文件已清理")
        }
    }
    
    /// 导出监控数据
    func exportData(to url: URL, days: Int = 7) -> Bool {
        let scheduler = SystemMonitorScheduler.shared
        
        do {
            let metrics = scheduler.getRecentMetrics(days: days)
            let appUsage = scheduler.getAppUsageSummary(days: days)
            
            let exportData: [String: Any] = [
                "export_date": Date().timeIntervalSince1970,
                "days": days,
                "metrics": metrics,
                "app_usage": appUsage,
                "system_info": getMonitoringStatus()
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: exportData, options: .prettyPrinted)
            try jsonData.write(to: url)
            
            Logger.info("监控数据已导出到: \(url.path)")
            return true
        } catch {
            Logger.error("数据导出失败: \(error)")
            return false
        }
    }
    
    // MARK: - 辅助方法
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - 扩展Logger以支持开发/生产环境配置

extension Logger {
    /// 开发环境配置
    static func setupForDevelopment() {
        setLogLevel(.debug)
        setConsoleLogging(enabled: true)
        setFileLogging(enabled: true)
        setPerformanceTracking(enabled: true)
        info("日志系统已配置为开发模式")
    }
    
    /// 生产环境配置
    static func setupForProduction() {
        setLogLevel(.info)
        setConsoleLogging(enabled: false)
        setPerformanceTracking(enabled: false)
        setFileLogging(enabled: true)
        info("日志系统已配置为生产模式")
    }
}
