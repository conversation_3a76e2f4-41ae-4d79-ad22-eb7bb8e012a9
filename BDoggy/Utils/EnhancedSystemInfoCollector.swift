//
//  EnhancedSystemInfoCollector.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import AppKit
import Cocoa
import Foundation
import IOKit
import IOKit.ps
import IOKit.pwr_mgt
import Darwin

/// 系统信息采集错误类型
enum SystemInfoCollectionError: Error, LocalizedError {
    case permissionDenied(String)
    case systemCallFailed(String)
    case dataCorrupted(String)
    case resourceUnavailable(String)
    case timeout(String)

    var errorDescription: String? {
        switch self {
        case .permissionDenied(let message):
            return "权限不足: \(message)"
        case .systemCallFailed(let message):
            return "系统调用失败: \(message)"
        case .dataCorrupted(let message):
            return "数据损坏: \(message)"
        case .resourceUnavailable(let message):
            return "资源不可用: \(message)"
        case .timeout(let message):
            return "操作超时: \(message)"
        }
    }
}

/// 增强版系统信息采集器
/// 使用IOKit、sysctl、NSWorkspace等原生API采集系统信息
class EnhancedSystemInfoCollector {
    // MARK: - 错误处理和重试机制

    /// 执行带重试机制的操作
    private static func executeWithRetry<T>(
        operation: () throws -> T,
        maxRetries: Int = 3,
        retryDelay: TimeInterval = 0.1,
        operationName: String
    ) -> Result<T, SystemInfoCollectionError> {
        var lastError: Error?

        for attempt in 1 ... maxRetries {
            do {
                let result = try operation()
                if attempt > 1 {
                    Logger.info("操作 \(operationName) 在第 \(attempt) 次尝试后成功")
                }
                return .success(result)
            } catch {
                lastError = error
                Logger.warning("操作 \(operationName) 第 \(attempt) 次尝试失败: \(error.localizedDescription)")

                if attempt < maxRetries {
                    Thread.sleep(forTimeInterval: retryDelay)
                }
            }
        }

        let errorMessage = "操作 \(operationName) 在 \(maxRetries) 次尝试后仍然失败"
        Logger.error(errorMessage)

        if let error = lastError as? SystemInfoCollectionError {
            return .failure(error)
        } else {
            return .failure(.systemCallFailed("\(errorMessage): \(lastError?.localizedDescription ?? "未知错误")"))
        }
    }

    /// 安全执行系统调用
    private static func safeSystemCall<T>(
        operation: () -> T,
        fallbackValue: T,
        operationName: String
    ) -> T {
        let result = executeWithRetry(
            operation: operation,
            operationName: operationName
        )

        switch result {
        case .success(let value):
            return value
        case .failure(let error):
            Logger.error("系统调用失败，使用默认值: \(error.localizedDescription)")
            return fallbackValue
        }
    }

    // MARK: - CPU信息采集

    /// 获取CPU使用率
    static func getCPUUsage() -> Double {
        let metrics = getDetailedCPUMetrics()
        return metrics.usagePercent
    }

    /// 获取详细的CPU指标数据
    static func getDetailedCPUMetrics() -> CPUMetricsModel {
        var cpuInfo: processor_info_array_t!
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0

        let result = host_processor_info(mach_host_self(), PROCESSOR_CPU_LOAD_INFO, &numCpus, &cpuInfo, &numCpuInfo)

        guard result == KERN_SUCCESS else {
            Logger.error("获取CPU信息失败")
            return CPUMetricsModel(
                usagePercent: 0.0,
                userPercent: 0.0,
                systemPercent: 0.0,
                idlePercent: 100.0,
                coreCount: getCPUCoreCount()
            )
        }

        defer {
            let size = vm_size_t(numCpuInfo) * vm_size_t(MemoryLayout<integer_t>.size)
            vm_deallocate(mach_task_self_, vm_address_t(bitPattern: cpuInfo), size)
        }

        var totalUser: UInt32 = 0
        var totalSystem: UInt32 = 0
        var totalIdle: UInt32 = 0
        var totalNice: UInt32 = 0

        for i in 0 ..< Int(numCpus) {
            let cpuLoadInfo = cpuInfo.advanced(by: i * Int(CPU_STATE_MAX)).withMemoryRebound(to: UInt32.self, capacity: Int(CPU_STATE_MAX)) { $0 }

            totalUser += cpuLoadInfo[Int(CPU_STATE_USER)]
            totalSystem += cpuLoadInfo[Int(CPU_STATE_SYSTEM)]
            totalIdle += cpuLoadInfo[Int(CPU_STATE_IDLE)]
            totalNice += cpuLoadInfo[Int(CPU_STATE_NICE)]
        }

        let totalTicks = totalUser + totalSystem + totalIdle + totalNice

        guard totalTicks > 0 else {
            return CPUMetricsModel(
                usagePercent: 0.0,
                userPercent: 0.0,
                systemPercent: 0.0,
                idlePercent: 100.0,
                coreCount: getCPUCoreCount()
            )
        }

        let userPercent = Double(totalUser) / Double(totalTicks) * 100.0
        let systemPercent = Double(totalSystem) / Double(totalTicks) * 100.0
        let idlePercent = Double(totalIdle) / Double(totalTicks) * 100.0
        let usagePercent = userPercent + systemPercent + (Double(totalNice) / Double(totalTicks) * 100.0)

        // 获取负载平均值
        let loadAverages = getLoadAverages()

        // 获取CPU频率
        let frequency = getCPUFrequency()

        return CPUMetricsModel(
            usagePercent: usagePercent,
            userPercent: userPercent,
            systemPercent: systemPercent,
            idlePercent: idlePercent,
            loadAvg1min: loadAverages.0,
            loadAvg5min: loadAverages.1,
            loadAvg15min: loadAverages.2,
            coreCount: getCPUCoreCount(),
            frequencyMHz: frequency
        )
    }

    /// 获取系统负载平均值
    static func getLoadAverages() -> (Double?, Double?, Double?) {
        var loadAvg = [Double](repeating: 0.0, count: 3)
        let result = getloadavg(&loadAvg, 3)

        guard result == 3 else {
            Logger.warning("获取负载平均值失败")
            return (nil, nil, nil)
        }

        return (loadAvg[0], loadAvg[1], loadAvg[2])
    }

    /// 获取CPU频率（MHz）
    static func getCPUFrequency() -> Int? {
        var size = MemoryLayout<UInt64>.size
        var frequency: UInt64 = 0

        if sysctlbyname("hw.cpufrequency_max", &frequency, &size, nil, 0) == 0 {
            return Int(frequency / 1000000) // 转换为MHz
        }

        // 备用方法：尝试获取当前频率
        if sysctlbyname("hw.cpufrequency", &frequency, &size, nil, 0) == 0 {
            return Int(frequency / 1000000)
        }

        return nil
    }

    // MARK: - CPU 名称

    static func getCPUName() -> String {
        var size = 0
        sysctlbyname("machdep.cpu.brand_string", nil, &size, nil, 0)
        var cpuName = [CChar](repeating: 0, count: size)
        sysctlbyname("machdep.cpu.brand_string", &cpuName, &size, nil, 0)
        return String(cString: cpuName)
    }

    /// 获取CPU核心数
    static func getCPUCoreCount() -> Int {
        var size = MemoryLayout<Int>.size
        var coreCount = 0

        if sysctlbyname("hw.ncpu", &coreCount, &size, nil, 0) == 0 {
            return coreCount
        }

        return 1
    }

    /// 获取CPU温度
    static func getCPUTemperature() -> Double? {
        // 使用IOKit获取CPU温度
        let service = IOServiceGetMatchingService(kIOMainPortDefault, IOServiceMatching("IOPMrootDomain"))
        guard service != 0 else { return nil }

        defer { IOObjectRelease(service) }

        // 尝试获取温度传感器数据
        // 注意：这个方法在不同的Mac型号上可能有所不同
        return getThermalState()
    }

    private static func getThermalState() -> Double? {
        var size = MemoryLayout<UInt32>.size
        var thermalState: UInt32 = 0

        if sysctlbyname("machdep.xcpm.cpu_thermal_level", &thermalState, &size, nil, 0) == 0 {
            // 将热状态转换为大概的温度值
            return Double(thermalState) * 10.0 + 40.0 // 粗略估算
        }

        return nil
    }

    /// 获取CPU架构
    static func getCPUArchitecture() -> String {
        var size = 0
        sysctlbyname("hw.targettype", nil, &size, nil, 0)
        var targetType = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.targettype", &targetType, &size, nil, 0)
        let target = String(cString: targetType)

        // 如果无法获取targettype，尝试其他方法
        if target.isEmpty {
            #if arch(arm64)
            return "Apple Silicon"
            #elseif arch(x86_64)
            return "Intel x86_64"
            #else
            return "Unknown"
            #endif
        }

        return target
    }

    // MARK: - 内存信息采集

    /// 获取内存信息
    static func getMemoryInfo() -> MemoryInfo {
        let metrics = getDetailedMemoryMetrics()
        return MemoryInfo(
            usagePercent: metrics.usagePercent,
            usedGB: Double(metrics.usedMB) / 1024.0,
            availableGB: Double(metrics.availableMB) / 1024.0,
            totalGB: Double(metrics.totalMB) / 1024.0,
            swapUsedGB: Double(metrics.swapUsedMB) / 1024.0,
            swapTotalGB: Double(metrics.swapTotalMB) / 1024.0
        )
    }

    /// 获取详细的内存指标数据
    static func getDetailedMemoryMetrics() -> MemoryMetricsModel {
        var vmStat = vm_statistics64()
        var count = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)

        let result = withUnsafeMutablePointer(to: &vmStat) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                host_statistics64(mach_host_self(), HOST_VM_INFO64, $0, &count)
            }
        }

        guard result == KERN_SUCCESS else {
            Logger.error("获取内存统计失败")
            return MemoryMetricsModel(
                usagePercent: 0.0,
                usedMB: 0,
                availableMB: 0,
                totalMB: 0,
                activeMB: 0,
                inactiveMB: 0,
                wiredMB: 0,
                compressedMB: 0,
                swapUsedMB: 0,
                swapTotalMB: 0,
                memoryPressure: 0
            )
        }

        let pageSize = vm_kernel_page_size
        let bytesToMB = { (bytes: UInt64) -> Int in
            return Int(bytes / (1024 * 1024))
        }

        // 计算各种内存类型的大小（MB）
        let activeMB = bytesToMB(UInt64(vmStat.active_count) * UInt64(pageSize))
        let inactiveMB = bytesToMB(UInt64(vmStat.inactive_count) * UInt64(pageSize))
        let wiredMB = bytesToMB(UInt64(vmStat.wire_count) * UInt64(pageSize))
        let compressedMB = bytesToMB(UInt64(vmStat.compressor_page_count) * UInt64(pageSize))
        let freeMB = bytesToMB(UInt64(vmStat.free_count) * UInt64(pageSize))

        let totalMB = activeMB + inactiveMB + wiredMB + compressedMB + freeMB
        let usedMB = activeMB + inactiveMB + wiredMB + compressedMB
        let availableMB = freeMB + inactiveMB // 可用内存包括空闲和非活跃内存

        let usagePercent = totalMB > 0 ? (Double(usedMB) / Double(totalMB)) * 100.0 : 0.0

        // 获取Swap信息
        let swapInfo = getSwapInfo()
        let swapUsedMB = Int(swapInfo.used * 1024) // 转换为MB
        let swapTotalMB = Int(swapInfo.total * 1024)

        // 计算内存压力
        let memoryPressure = calculateMemoryPressure(
            totalMB: totalMB,
            usedMB: usedMB,
            compressedMB: compressedMB,
            swapUsedMB: swapUsedMB
        )

        return MemoryMetricsModel(
            usagePercent: usagePercent,
            usedMB: usedMB,
            availableMB: availableMB,
            totalMB: totalMB,
            activeMB: activeMB,
            inactiveMB: inactiveMB,
            wiredMB: wiredMB,
            compressedMB: compressedMB,
            swapUsedMB: swapUsedMB,
            swapTotalMB: swapTotalMB,
            memoryPressure: memoryPressure
        )
    }

    /// 计算内存压力指数 (0-100)
    private static func calculateMemoryPressure(totalMB: Int, usedMB: Int, compressedMB: Int, swapUsedMB: Int) -> Int {
        guard totalMB > 0 else { return 0 }

        let usageRatio = Double(usedMB) / Double(totalMB)
        let compressionRatio = Double(compressedMB) / Double(totalMB)
        let swapRatio = swapUsedMB > 0 ? min(Double(swapUsedMB) / Double(totalMB), 1.0) : 0.0

        // 内存压力计算公式：基础使用率 + 压缩内存权重 + 交换区使用权重
        let pressure = (usageRatio * 60.0) + (compressionRatio * 25.0) + (swapRatio * 15.0)

        return min(Int(pressure * 100), 100)
    }

    private static func getSwapInfo() -> (used: Double, total: Double) {
        var swapUsage = xsw_usage()
        var size = MemoryLayout<xsw_usage>.size

        if sysctlbyname("vm.swapusage", &swapUsage, &size, nil, 0) == 0 {
            let usedGB = Double(swapUsage.xsu_used) / (1024 * 1024 * 1024)
            let totalGB = Double(swapUsage.xsu_total) / (1024 * 1024 * 1024)
            return (usedGB, totalGB)
        }

        return (0, 0)
    }

    // MARK: - 磁盘信息采集

    /// 获取磁盘信息
    static func getDiskInfo() -> DiskInfo {
        let metrics = getDetailedDiskMetrics()
        return DiskInfo(
            freeGB: Double(metrics.freeGB),
            usagePercent: metrics.usagePercent,
            totalGB: Double(metrics.totalGB)
        )
    }

    /// 获取详细的磁盘指标数据
    static func getDetailedDiskMetrics() -> DiskMetricsModel {
        let fileManager = FileManager.default

        do {
            let homeURL = fileManager.homeDirectoryForCurrentUser
            let resourceValues = try homeURL.resourceValues(forKeys: [
                .volumeTotalCapacityKey,
                .volumeAvailableCapacityKey
            ])

            let totalBytes = resourceValues.volumeTotalCapacity ?? 0
            let availableBytes = resourceValues.volumeAvailableCapacity ?? 0
            let usedBytes = totalBytes - availableBytes

            let totalGB = Int(totalBytes / (1024 * 1024 * 1024))
            let freeGB = Int(availableBytes / (1024 * 1024 * 1024))
            let usedGB = Int(usedBytes / (1024 * 1024 * 1024))
            let usagePercent = totalBytes > 0 ? (Double(usedBytes) / Double(totalBytes)) * 100.0 : 0.0

            // 获取磁盘I/O统计信息
            let ioStats = getDiskIOStats()

            return DiskMetricsModel(
                usagePercent: usagePercent,
                usedGB: usedGB,
                freeGB: freeGB,
                totalGB: totalGB,
                readOpsPerSec: ioStats.readOps,
                writeOpsPerSec: ioStats.writeOps,
                readBytesPerSec: ioStats.readBytes,
                writeBytesPerSec: ioStats.writeBytes
            )
        } catch {
            Logger.error("获取磁盘信息失败: \(error)")
            return DiskMetricsModel(
                usagePercent: 0.0,
                usedGB: 0,
                freeGB: 0,
                totalGB: 0,
                readOpsPerSec: nil,
                writeOpsPerSec: nil,
                readBytesPerSec: nil,
                writeBytesPerSec: nil
            )
        }
    }

    /// 获取磁盘I/O统计信息
    private static func getDiskIOStats() -> (readOps: Int?, writeOps: Int?, readBytes: Int?, writeBytes: Int?) {
        // 通过IOKit获取磁盘I/O统计信息
        var readOps: Int?
        var writeOps: Int?
        var readBytes: Int?
        var writeBytes: Int?

        // 获取IOBlockStorageDriver服务
        let matchingDict = IOServiceMatching("IOBlockStorageDriver")
        var iterator: io_iterator_t = 0

        let result = IOServiceGetMatchingServices(kIOMainPortDefault, matchingDict, &iterator)
        guard result == KERN_SUCCESS else {
            Logger.debug("无法获取磁盘I/O服务")
            return (nil, nil, nil, nil)
        }

        defer { IOObjectRelease(iterator) }

        var service: io_object_t = IOIteratorNext(iterator)
        var totalReadOps = 0
        var totalWriteOps = 0
        var totalReadBytes = 0
        var totalWriteBytes = 0

        while service != 0 {
            defer {
                IOObjectRelease(service)
                service = IOIteratorNext(iterator)
            }

            // 获取统计信息
            if let properties = getIOServiceProperties(service) {
                if let statistics = properties["Statistics"] as? [String: Any] {
                    if let operations = statistics["Operations"] as? [String: Any] {
                        totalReadOps += (operations["Read"] as? Int) ?? 0
                        totalWriteOps += (operations["Write"] as? Int) ?? 0
                    }

                    if let bytes = statistics["Bytes"] as? [String: Any] {
                        totalReadBytes += (bytes["Read"] as? Int) ?? 0
                        totalWriteBytes += (bytes["Write"] as? Int) ?? 0
                    }
                }
            }
        }

        // 如果获取到了数据，返回结果
        if totalReadOps > 0 || totalWriteOps > 0 || totalReadBytes > 0 || totalWriteBytes > 0 {
            readOps = totalReadOps
            writeOps = totalWriteOps
            readBytes = totalReadBytes
            writeBytes = totalWriteBytes
        }

        return (readOps, writeOps, readBytes, writeBytes)
    }

    /// 获取IOService属性
    private static func getIOServiceProperties(_ service: io_object_t) -> [String: Any]? {
        var properties: Unmanaged<CFMutableDictionary>?
        let result = IORegistryEntryCreateCFProperties(service, &properties, kCFAllocatorDefault, 0)

        guard result == KERN_SUCCESS, let props = properties?.takeRetainedValue() else {
            return nil
        }

        return props as? [String: Any]
    }

    // MARK: - 电池信息采集

    /// 获取电池信息
    static func getBatteryInfo() -> BatteryInfo {
        let powerSources = IOPSCopyPowerSourcesInfo()?.takeRetainedValue()
        let powerSourcesList = IOPSCopyPowerSourcesList(powerSources)?.takeRetainedValue() as? [CFDictionary]

        var batteryLevel: Int?
        var batteryHealth: Double?
        var cycleCount: Int?
        var powerSourceType = "AC Power"

        if let powerSourcesList = powerSourcesList {
            for powerSource in powerSourcesList {
                let psDict = powerSource as NSDictionary

                if let type = psDict[kIOPSTypeKey] as? String, type == kIOPSInternalBatteryType {
                    // 电池电量
                    if let currentCapacity = psDict[kIOPSCurrentCapacityKey] as? Int,
                       let maxCapacity = psDict[kIOPSMaxCapacityKey] as? Int, maxCapacity > 0
                    {
                        batteryLevel = (currentCapacity * 100) / maxCapacity
                    }

                    // 电池健康度
                    if let designCapacity = psDict["DesignCapacity"] as? Int,
                       let maxCapacity = psDict[kIOPSMaxCapacityKey] as? Int, designCapacity > 0
                    {
                        batteryHealth = (Double(maxCapacity) / Double(designCapacity)) * 100.0
                    }

                    // 循环次数
                    cycleCount = psDict["CycleCount"] as? Int

                    // 电源状态
                    if let powerSourceState = psDict[kIOPSPowerSourceStateKey] as? String {
                        powerSourceType = powerSourceState == kIOPSACPowerValue ? "AC Power" : "Battery"
                    }

                    break
                }
            }
        }

        return BatteryInfo(
            level: batteryLevel,
            health: batteryHealth,
            cycleCount: cycleCount,
            powerSourceType: powerSourceType
        )
    }

    // MARK: - 温度和风扇信息

    /// 获取GPU温度
    static func getGPUTemperature() -> Double? {
        // 尝试通过IOKit获取GPU温度
        // 不同的GPU可能有不同的服务名称
        let gpuServiceNames = [
            "IOAccelerator",
            "AppleIntelFramebuffer",
            "AMDRadeonX4000",
            "AMDRadeonX5000",
            "AMDRadeonX6000",
            "NVDA"
        ]

        for serviceName in gpuServiceNames {
            if let temperature = getTemperatureFromService(serviceName) {
                Logger.debug("GPU温度: \(String(format: "%.1f", temperature))°C (来源: \(serviceName))")
                return temperature
            }
        }

        // 如果无法获取GPU温度，尝试通过系统热状态估算
        if let thermalState = getThermalState() {
            // 基于系统热状态估算GPU温度（通常比CPU温度高5-10度）
            let estimatedGPUTemp = thermalState + 8.0
            Logger.debug("GPU温度估算: \(String(format: "%.1f", estimatedGPUTemp))°C (基于热状态)")
            return estimatedGPUTemp
        }

        Logger.debug("无法获取GPU温度")
        return nil
    }

    /// 从指定服务获取温度
    private static func getTemperatureFromService(_ serviceName: String) -> Double? {
        let matchingDict = IOServiceMatching(serviceName)
        var iterator: io_iterator_t = 0

        let result = IOServiceGetMatchingServices(kIOMainPortDefault, matchingDict, &iterator)
        guard result == KERN_SUCCESS else { return nil }

        defer { IOObjectRelease(iterator) }

        var service: io_object_t = IOIteratorNext(iterator)
        while service != 0 {
            defer {
                IOObjectRelease(service)
                service = IOIteratorNext(iterator)
            }

            // 尝试获取温度属性
            if let properties = getIOServiceProperties(service) {
                // 查找温度相关的属性
                let temperatureKeys = ["temperature", "Temperature", "temp", "Temp", "thermal"]

                for key in temperatureKeys {
                    if let tempValue = properties[key] as? NSNumber {
                        let temperature = tempValue.doubleValue
                        // 温度值可能需要转换（有些以毫度为单位）
                        if temperature > 1000 {
                            return temperature / 1000.0
                        } else if temperature > 0 && temperature < 200 {
                            return temperature
                        }
                    }
                }
            }
        }

        return nil
    }

    /// 获取风扇转速
    static func getFanSpeed() -> Int? {
        // 尝试通过IOKit获取风扇信息
        // 风扇信息通常在AppleSMC或者相关的热管理服务中

        let fanServiceNames = [
            "AppleSMC",
            "IOPMrootDomain",
            "AppleSMCFamily"
        ]

        for serviceName in fanServiceNames {
            if let fanSpeed = getFanSpeedFromService(serviceName) {
                Logger.debug("风扇转速: \(fanSpeed) RPM (来源: \(serviceName))")
                return fanSpeed
            }
        }

        // 如果无法获取实际风扇转速，尝试基于系统热状态估算
        if let thermalState = getThermalState() {
            // 基于热状态估算风扇转速
            let estimatedFanSpeed = estimateFanSpeedFromThermalState(thermalState)
            Logger.debug("风扇转速估算: \(estimatedFanSpeed) RPM (基于热状态)")
            return estimatedFanSpeed
        }

        Logger.debug("无法获取风扇转速")
        return nil
    }

    /// 从指定服务获取风扇转速
    private static func getFanSpeedFromService(_ serviceName: String) -> Int? {
        let matchingDict = IOServiceMatching(serviceName)
        var iterator: io_iterator_t = 0

        let result = IOServiceGetMatchingServices(kIOMainPortDefault, matchingDict, &iterator)
        guard result == KERN_SUCCESS else { return nil }

        defer { IOObjectRelease(iterator) }

        var service: io_object_t = IOIteratorNext(iterator)
        while service != 0 {
            defer {
                IOObjectRelease(service)
                service = IOIteratorNext(iterator)
            }

            if let properties = getIOServiceProperties(service) {
                // 查找风扇相关的属性
                let fanKeys = ["FanSpeed", "fan-speed", "Fan0", "Fan1", "RPM", "rpm"]

                for key in fanKeys {
                    if let fanValue = properties[key] as? NSNumber {
                        let speed = fanValue.intValue
                        if speed > 0 && speed < 10000 { // 合理的风扇转速范围
                            return speed
                        }
                    }
                }

                // 查找嵌套的风扇信息
                if let fanInfo = properties["FanInfo"] as? [String: Any] {
                    for (_, value) in fanInfo {
                        if let fanSpeed = value as? NSNumber {
                            let speed = fanSpeed.intValue
                            if speed > 0 && speed < 10000 {
                                return speed
                            }
                        }
                    }
                }
            }
        }

        return nil
    }

    /// 基于热状态估算风扇转速
    private static func estimateFanSpeedFromThermalState(_ thermalState: Double) -> Int {
        // 基于温度估算风扇转速
        // 这是一个粗略的估算，实际风扇控制算法会更复杂
        switch thermalState {
        case 0..<50:
            return 1200 // 低温，低转速
        case 50..<65:
            return 2000 // 中温，中转速
        case 65..<80:
            return 3500 // 高温，高转速
        default:
            return 5000 // 过热，最高转速
        }
    }

    // MARK: - 当前活跃应用

    /// 获取当前活跃应用
    static func getActiveApplication() -> ActiveAppInfo {
        let workspace = NSWorkspace.shared

        if let frontmostApp = workspace.frontmostApplication {
            return ActiveAppInfo(
                bundleID: frontmostApp.bundleIdentifier,
                name: frontmostApp.localizedName
            )
        }

        return ActiveAppInfo(bundleID: nil, name: nil)
    }

    // MARK: - 系统负载

    /// 获取系统负载平均值
    static func getLoadAverage() -> LoadAverageInfo {
        var loadAvg: [Double] = [0, 0, 0]

        if getloadavg(&loadAvg, 3) != -1 {
            return LoadAverageInfo(
                oneMin: loadAvg[0],
                fiveMin: loadAvg[1],
                fifteenMin: loadAvg[2]
            )
        }

        return LoadAverageInfo(oneMin: 0, fiveMin: 0, fifteenMin: 0)
    }

    // MARK: - 网络信息

    /// 获取网络信息
    static func getNetworkInfo() -> NetworkInfo {
        let metrics = getDetailedNetworkMetrics()
        return NetworkInfo(
            bytesIn: UInt64(metrics.bytesIn),
            bytesOut: UInt64(metrics.bytesOut)
        )
    }

    /// 获取详细的网络指标数据
    static func getDetailedNetworkMetrics() -> NetworkMetricsModel {
        var bytesIn: Int64 = 0
        var bytesOut: Int64 = 0
        var packetsIn: Int64 = 0
        var packetsOut: Int64 = 0
        var errorsIn = 0
        var errorsOut = 0
        var interfaceName: String?

        // 获取网络接口统计信息
        var ifaddrs: UnsafeMutablePointer<ifaddrs>?

        guard getifaddrs(&ifaddrs) == 0 else {
            Logger.error("获取网络接口信息失败")
            return NetworkMetricsModel(
                bytesIn: 0,
                bytesOut: 0,
                packetsIn: 0,
                packetsOut: 0,
                errorsIn: 0,
                errorsOut: 0,
                interfaceName: nil
            )
        }

        defer {
            freeifaddrs(ifaddrs)
        }

        var ptr = ifaddrs
        while ptr != nil {
            let interface = ptr!.pointee
            let name = String(cString: interface.ifa_name)

            // 只统计活跃的网络接口，排除回环接口
            if interface.ifa_addr != nil &&
                interface.ifa_data != nil &&
                !name.hasPrefix("lo") &&
                (interface.ifa_flags & UInt32(IFF_UP)) != 0 &&
                (interface.ifa_flags & UInt32(IFF_RUNNING)) != 0
            {
                let data = interface.ifa_data.assumingMemoryBound(to: if_data.self)

                bytesIn += Int64(data.pointee.ifi_ibytes)
                bytesOut += Int64(data.pointee.ifi_obytes)
                packetsIn += Int64(data.pointee.ifi_ipackets)
                packetsOut += Int64(data.pointee.ifi_opackets)
                errorsIn += Int(data.pointee.ifi_ierrors)
                errorsOut += Int(data.pointee.ifi_oerrors)

                // 记录主要网络接口名称
                if interfaceName == nil && (name.hasPrefix("en") || name.hasPrefix("wi")) {
                    interfaceName = name
                }
            }

            ptr = interface.ifa_next
        }

        return NetworkMetricsModel(
            bytesIn: bytesIn,
            bytesOut: bytesOut,
            packetsIn: packetsIn,
            packetsOut: packetsOut,
            errorsIn: errorsIn,
            errorsOut: errorsOut,
            interfaceName: interfaceName
        )
    }

    // MARK: - 进程信息

    /// 获取Top进程信息
    static func getTopProcesses(limit: Int = 10) -> [ProcessInfoModel] {
        var processes: [ProcessInfoModel] = []

        // 获取所有进程的PID列表
        var size: size_t = 0
        var mib: [Int32] = [CTL_KERN, KERN_PROC, KERN_PROC_ALL, 0]
        var result = sysctl(&mib, 4, nil, &size, nil, 0)

        guard result == 0 else {
            Logger.error("无法获取进程列表大小")
            return []
        }

        let count = size / MemoryLayout<kinfo_proc>.size
        var procs = Array<kinfo_proc>(repeating: kinfo_proc(), count: count)

        result = sysctl(&mib, 4, &procs, &size, nil, 0)
        guard result == 0 else {
            Logger.error("无法获取进程列表")
            return []
        }

        // 处理每个进程
        for proc in procs.prefix(count) {
            let pid = proc.kp_proc.p_pid

            // 跳过系统进程和无效进程
            guard pid > 0 else { continue }

            // 获取进程名称
            let processName = withUnsafePointer(to: proc.kp_proc.p_comm) {
                $0.withMemoryRebound(to: CChar.self, capacity: MemoryLayout.size(ofValue: proc.kp_proc.p_comm)) {
                    String(cString: $0)
                }
            }

            // 获取进程的CPU和内存使用情况
            if let processInfo = getProcessResourceUsage(pid: pid) {
                let process = ProcessInfoModel(
                    pid: Int32(pid),
                    name: processName,
                    cpuUsage: processInfo.cpuUsage,
                    memoryUsageMB: Double(processInfo.memoryUsageMB), // 转换为Double类型
                    bundleID: nil // 暂时不获取bundleID，可以后续优化
                )
                processes.append(process)
            }
        }

        // 按CPU使用率排序并返回前N个
        processes.sort { $0.cpuUsage > $1.cpuUsage }
        return Array(processes.prefix(limit))
    }

    /// 获取指定进程的资源使用情况
    private static func getProcessResourceUsage(pid: pid_t) -> (cpuUsage: Double, memoryUsageMB: Int)? {
        var taskInfo = task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<task_basic_info>.size / MemoryLayout<integer_t>.size)

        var task: mach_port_t = 0
        let taskResult = task_for_pid(mach_task_self_, pid, &task)
        guard taskResult == KERN_SUCCESS else { return nil }

        let result = withUnsafeMutablePointer(to: &taskInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(task, task_flavor_t(TASK_BASIC_INFO), $0, &count)
            }
        }

        guard result == KERN_SUCCESS else { return nil }

        // 计算内存使用量（转换为MB）
        let memoryUsageMB = Int(taskInfo.resident_size) / (1024 * 1024)

        // 获取CPU使用率需要更复杂的计算，这里提供一个简化版本
        let cpuUsage = getCPUUsageForProcess(pid: pid)

        return (cpuUsage: cpuUsage, memoryUsageMB: memoryUsageMB)
    }

    /// 获取进程CPU使用率（简化版本）
    private static func getCPUUsageForProcess(pid: pid_t) -> Double {
        // 这是一个简化的实现，实际的CPU使用率计算需要时间间隔采样
        // 这里返回一个基于进程状态的估算值

        var procInfo = proc_taskinfo()
        let size = MemoryLayout<proc_taskinfo>.size

        let result = proc_pidinfo(pid, PROC_PIDTASKINFO, 0, &procInfo, Int32(size))
        guard result == size else { return 0.0 }

        // 基于任务信息估算CPU使用率
        // 这是一个简化的计算，实际应用中可能需要更精确的方法
        let totalTime = procInfo.pti_total_user + procInfo.pti_total_system

        // 将纳秒转换为秒，然后计算使用率
        let timeInSeconds = Double(totalTime) / 1_000_000_000.0

        // 这里返回一个相对值，实际使用率需要基于时间间隔计算
        return min(timeInSeconds / 100.0, 100.0)
    }

    // MARK: - 系统属性采集

    // MARK: - 设备型号

    static func getDeviceModel() -> String {
        var size = 0
        sysctlbyname("hw.model", nil, &size, nil, 0)
        var model = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.model", &model, &size, nil, 0)
        return String(cString: model)
    }

    // MARK: - macOS 版本

    static func getMacOSVersion() -> String {
        let version = ProcessInfo.processInfo.operatingSystemVersion

        return "\(version.majorVersion).\(version.minorVersion).\(version.patchVersion)"
    }

    // MARK: - 系统启动时间

    static func getSystemBootTime() -> Date {
        var size = MemoryLayout<timeval>.size
        var bootTime = timeval()
        sysctlbyname("kern.boottime", &bootTime, &size, nil, 0)
        return Date(timeIntervalSince1970: Double(bootTime.tv_sec))
    }

    // MARK: - 系统运行时间 (秒)

    static func getSystemUptime() -> TimeInterval {
        let bootTime = getSystemBootTime()
        return Date().timeIntervalSince(bootTime)
    }

    /// 获取内核版本
    static func getKernelVersion() -> String {
        var size = 0
        sysctlbyname("kern.version", nil, &size, nil, 0)
        var version = [CChar](repeating: 0, count: size)
        sysctlbyname("kern.version", &version, &size, nil, 0)
        let fullVersion = String(cString: version)

        // 提取版本号部分
        let components = fullVersion.components(separatedBy: " ")
        if components.count > 2 {
            return components[2] // 通常是第三个部分
        }

        return fullVersion.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// 获取系统序列号
    static func getSystemSerial() -> String {
        let service = IOServiceGetMatchingService(kIOMainPortDefault, IOServiceMatching("IOPlatformExpertDevice"))

        guard service != 0 else {
            return "Unknown"
        }

        defer { IOObjectRelease(service) }

        if let serialNumber = IORegistryEntryCreateCFProperty(service, "IOPlatformSerialNumber" as CFString, kCFAllocatorDefault, 0)?.takeRetainedValue() as? String {
            return serialNumber
        }

        return "Unknown"
    }

    // MARK: - 获取完整系统信息

    static func getCompleteSystemInfo() -> [String: Any] {
        var systemInfo: [String: Any] = [:]

        // 基本系统信息
        systemInfo["device_model"] = getDeviceModel()
        systemInfo["cpu_name"] = getCPUName()
        systemInfo["cpu_core_count"] = getCPUCoreCount()
        systemInfo["total_memory_gb"] = getMemoryInfo().totalGB
        systemInfo["total_disk_gb"] = getDiskInfo().totalGB
        systemInfo["macos_version"] = getMacOSVersion()
        systemInfo["kernel_version"] = getKernelVersion()
        systemInfo["system_serial"] = getSystemSerial()
        systemInfo["system_uptime"] = getSystemUptime()

        // 当前系统状态
        let cpuMetrics = getDetailedCPUMetrics()
        systemInfo["current_cpu_usage"] = cpuMetrics.usagePercent
        systemInfo["current_cpu_temperature"] = getCPUTemperature()

        let memoryMetrics = getDetailedMemoryMetrics()
        systemInfo["current_memory_usage"] = memoryMetrics.usagePercent
        systemInfo["current_memory_used_gb"] = Double(memoryMetrics.usedMB) / 1024.0

        let diskMetrics = getDetailedDiskMetrics()
        systemInfo["current_disk_usage"] = diskMetrics.usagePercent
        systemInfo["current_disk_free_gb"] = diskMetrics.freeGB

        // 电池信息（如果有）
        let batteryInfo = getBatteryInfo()
        if let level = batteryInfo.level {
            systemInfo["battery_level"] = level
            systemInfo["battery_health"] = batteryInfo.health
            systemInfo["battery_cycle_count"] = batteryInfo.cycleCount
            systemInfo["power_source"] = batteryInfo.powerSourceType
        }

        // 网络信息
        let networkMetrics = getDetailedNetworkMetrics()
        systemInfo["network_bytes_in"] = networkMetrics.bytesIn
        systemInfo["network_bytes_out"] = networkMetrics.bytesOut
        systemInfo["network_interface"] = networkMetrics.interfaceName

        // 温度和风扇信息
        systemInfo["gpu_temperature"] = getGPUTemperature()
        systemInfo["fan_speed"] = getFanSpeed()

        // 当前活跃应用
        let activeApp = getActiveApplication()
        systemInfo["active_app_bundle_id"] = activeApp.bundleID
        systemInfo["active_app_name"] = activeApp.name

        // Top进程信息
        let topProcesses = getTopProcesses(limit: 5)
        systemInfo["top_processes"] = topProcesses.map { process in
            return [
                "pid": process.pid,
                "name": process.name,
                "cpu_usage": process.cpuUsage,
                "memory_usage_mb": process.memoryUsageMB
            ]
        }

        // 系统负载
        systemInfo["load_average"] = getSystemLoadAverage()

        // 收集时间戳
        systemInfo["collected_at"] = Date().timeIntervalSince1970
        systemInfo["collected_at_iso"] = ISO8601DateFormatter().string(from: Date())

        Logger.info("完整系统信息收集完成，包含 \(systemInfo.keys.count) 个字段")

        return systemInfo
    }

    /// 获取系统负载平均值
    private static func getSystemLoadAverage() -> [String: Double] {
        var loadAvg = [Double](repeating: 0.0, count: 3)
        let result = getloadavg(&loadAvg, 3)

        if result == 3 {
            return [
                "1min": loadAvg[0],
                "5min": loadAvg[1],
                "15min": loadAvg[2]
            ]
        }

        return [
            "1min": 0.0,
            "5min": 0.0,
            "15min": 0.0
        ]
    }
}
