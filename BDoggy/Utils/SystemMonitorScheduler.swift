//
//  SystemMonitorScheduler.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import AppKit
import Combine
import Foundation

/// 系统监控调度器
/// 负责定时采集系统指标并存储到数据库
@Observable
class SystemMonitorScheduler {
    static let shared = SystemMonitorScheduler()

    // MARK: - 属性

    private var metricsTimer: Timer?
    private var appUsageTimer: Timer?
    private var maintenanceTimer: Timer?
    private var performanceTimer: Timer?
    private var currentAppUsage: AppUsageRecord?
    private var appUsages: [String: AppUsageRecord] = [:]
    private var lastActiveApp: String?
    private var lastAppSwitchTime: Date?

    let database = OptimizedSystemMonitorDatabase.shared
    private let userDefaults = UserDefaults.standard

    // MARK: - 线程安全

    private let schedulerQueue = DispatchQueue(label: "com.bdoggy.scheduler", qos: .utility)
    private let appUsageQueue = DispatchQueue(label: "com.bdoggy.appusage", qos: .utility)
    private let dataCollectionQueue = DispatchQueue(label: "com.bdoggy.datacollection", qos: .utility)

    // 线程安全的属性访问
    private let lockQueue = DispatchQueue(label: "com.bdoggy.scheduler.lock", qos: .userInitiated)
    private var _isMonitoring = false
    private var _totalDataPoints = 0
    private var _lastCollectionTime: Date?

    var isMonitoring: Bool {
        get { lockQueue.sync { _isMonitoring } }
        set { lockQueue.sync { _isMonitoring = newValue } }
    }

    var totalDataPoints: Int {
        get { lockQueue.sync { _totalDataPoints } }
        set { lockQueue.sync { _totalDataPoints = newValue } }
    }

    var lastCollectionTime: Date? {
        get { lockQueue.sync { _lastCollectionTime } }
        set { lockQueue.sync { _lastCollectionTime = newValue } }
    }

    // 配置
    var config = MonitorConfig() {
        didSet {
            saveConfig()
            restartMonitoring()
        }
    }

    // 状态
    var databaseSize: Int64 = 0
    var saveCount: Int64 = 0

    // 权限状态
    var hasAccessibilityPermission = false

    // MARK: - 初始化

    private init() {
        loadConfig()
        checkPermissions()
    }

    func initialize() {
        setupNotifications()

        // 初始化时记录系统信息（只记录一次）
        recordSystemInfoIfNeeded()
    }

    deinit {
        stopMonitoring()
    }

    // MARK: - 配置管理

    private func loadConfig() {
        if let data = userDefaults.data(forKey: "SystemMonitorConfig"),
           let savedConfig = try? JSONDecoder().decode(MonitorConfig.self, from: data)
        {
            config = savedConfig
        }
    }

    private func saveConfig() {
        if let data = try? JSONEncoder().encode(config) {
            userDefaults.set(data, forKey: "SystemMonitorConfig")
        }
    }

    // MARK: - 权限检查

    private func checkPermissions() {
        // 检查辅助功能权限（用于获取当前活跃应用）
        hasAccessibilityPermission = AXIsProcessTrusted()
    }

    /// 请求辅助功能权限
    func requestAccessibilityPermission() {
        let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue(): true]
        AXIsProcessTrustedWithOptions(options as CFDictionary)

        // 延迟检查权限状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.checkPermissions()
        }
    }

    // MARK: - 监控控制

    /// 开始监控
    func startMonitoring() {
        guard !isMonitoring else { return }

        Logger.info("开始系统监控，采集间隔: \(config.collectionInterval)秒")

        // 立即采集一次数据
        collectMetrics()

        // 设置定时器
        metricsTimer = Timer.scheduledTimer(withTimeInterval: config.collectionInterval, repeats: true) { _ in
            self.collectMetrics()
        }

        // 如果启用应用使用跟踪，设置应用监控
        if config.enableAppUsageTracking {
            startAppUsageTracking()
        }

        // 启动定期维护任务（每天执行一次）
        startMaintenanceScheduler()

        // 启动性能优化调度（每小时执行一次）
        startPerformanceOptimizationScheduler()

        isMonitoring = true
        updateStatus()
    }

    /// 停止监控
    func stopMonitoring() {
        guard isMonitoring else { return }

        Logger.info("停止系统监控")

        metricsTimer?.invalidate()
        metricsTimer = nil

        appUsageTimer?.invalidate()
        appUsageTimer = nil

        maintenanceTimer?.invalidate()
        maintenanceTimer = nil

        performanceTimer?.invalidate()
        performanceTimer = nil

        // 结束当前应用使用记录
        endCurrentAppUsage()

        isMonitoring = false
        updateStatus()
    }

    /// 重启监控（配置更改时）
    private func restartMonitoring() {
        if isMonitoring {
            stopMonitoring()
            startMonitoring()
        }
    }

    // MARK: - 数据采集

    /// 采集系统指标
    private func collectMetrics() {
        guard isMonitoring else { return }

        let startTime = CFAbsoluteTimeGetCurrent()
        Logger.debug("开始采集系统指标...")

        // 使用专用队列进行数据采集，提高性能和线程安全
        dataCollectionQueue.async { [weak self] in
            guard let self = self else { return }

            do {
                // 采集各类指标
                let cpuMetrics = self.collectCPUMetrics()
                let memoryMetrics = self.collectMemoryMetrics()
                let diskMetrics = self.collectDiskMetrics()
                let batteryMetrics = self.collectBatteryMetrics()
                let thermalMetrics = self.collectThermalMetrics()
                let networkMetrics = self.collectNetworkMetrics()

                // 批量存储到数据库
                let result = self.database.insertMetricsBatch(
                    cpuMetrics: cpuMetrics != nil ? [cpuMetrics!] : [],
                    memoryMetrics: memoryMetrics != nil ? [memoryMetrics!] : [],
                    diskMetrics: diskMetrics != nil ? [diskMetrics!] : [],
                    batteryMetrics: batteryMetrics != nil ? [batteryMetrics!] : [],
                    thermalMetrics: thermalMetrics != nil ? [thermalMetrics!] : [],
                    networkMetrics: networkMetrics != nil ? [networkMetrics!] : []
                )

                switch result {
                case .success():
                    let executionTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
                    Logger.debug("系统指标采集完成，耗时: \(String(format: "%.2f", executionTime))ms")

                    DispatchQueue.main.async {
                        self.lastCollectionTime = Date()
                        self.totalDataPoints += 1
                        self.updateStatus()
                    }

                case .failure(let error):
                    Logger.error("存储系统指标失败: \(error)")
                }

            } catch {
                Logger.error("采集系统指标时发生错误: \(error)")
            }
        }
    }

    /// 采集CPU指标
    private func collectCPUMetrics() -> CPUMetricsModel? {
        do {
            let metrics = EnhancedSystemInfoCollector.getDetailedCPUMetrics()
            Logger.debug("CPU使用率: \(String(format: "%.1f", metrics.usagePercent))%")
            return metrics
        } catch {
            Logger.error("采集CPU指标失败: \(error)")
            return nil
        }
    }

    /// 采集内存指标
    private func collectMemoryMetrics() -> MemoryMetricsModel? {
        do {
            let metrics = EnhancedSystemInfoCollector.getDetailedMemoryMetrics()
            Logger.debug("内存使用率: \(String(format: "%.1f", metrics.usagePercent))%")
            return metrics
        } catch {
            Logger.error("采集内存指标失败: \(error)")
            return nil
        }
    }

    /// 采集磁盘指标
    private func collectDiskMetrics() -> DiskMetricsModel? {
        do {
            let metrics = EnhancedSystemInfoCollector.getDetailedDiskMetrics()
            Logger.debug("磁盘使用率: \(String(format: "%.1f", metrics.usagePercent))%")
            return metrics
        } catch {
            Logger.error("采集磁盘指标失败: \(error)")
            return nil
        }
    }

    /// 采集电池指标
    private func collectBatteryMetrics() -> BatteryMetricsModel? {
        do {
            let batteryInfo = EnhancedSystemInfoCollector.getBatteryInfo()

            // 转换为数据库模型
            let metrics = BatteryMetricsModel(
                levelPercent: batteryInfo.level,
                healthPercent: batteryInfo.health != nil ? Int(batteryInfo.health!) : nil,
                cycleCount: batteryInfo.cycleCount,
                powerSourceType: batteryInfo.powerSourceType,
                isCharging: batteryInfo.powerSourceType == "AC Power",
                timeRemainingMinutes: nil, // 需要额外实现
                temperatureCelsius: nil, // 需要额外实现
                voltageMV: nil // 需要额外实现
            )

            if let level = metrics.levelPercent {
                Logger.debug("电池电量: \(level)%")
            }
            return metrics
        } catch {
            Logger.error("采集电池指标失败: \(error)")
            return nil
        }
    }

    /// 采集温度指标
    private func collectThermalMetrics() -> ThermalMetricsModel? {
        do {
            let cpuTemp = EnhancedSystemInfoCollector.getCPUTemperature()
            let gpuTemp = EnhancedSystemInfoCollector.getGPUTemperature()
            let fanSpeed = EnhancedSystemInfoCollector.getFanSpeed()

            let metrics = ThermalMetricsModel(
                cpuTemperatureCelsius: cpuTemp,
                gpuTemperatureCelsius: gpuTemp,
                fanSpeedRPM: fanSpeed,
                thermalState: determineThermalState(cpuTemp: cpuTemp)
            )

            if let temp = cpuTemp {
                Logger.debug("CPU温度: \(String(format: "%.1f", temp))°C")
            }
            return metrics
        } catch {
            Logger.error("采集温度指标失败: \(error)")
            return nil
        }
    }

    /// 采集网络指标
    private func collectNetworkMetrics() -> NetworkMetricsModel? {
        do {
            let metrics = EnhancedSystemInfoCollector.getDetailedNetworkMetrics()
            Logger.debug("网络流量 - 入: \(formatBytes(metrics.bytesIn)), 出: \(formatBytes(metrics.bytesOut))")
            return metrics
        } catch {
            Logger.error("采集网络指标失败: \(error)")
            return nil
        }
    }

    /// 确定热状态
    private func determineThermalState(cpuTemp: Double?) -> String {
        guard let temp = cpuTemp else { return "Unknown" }

        switch temp {
        case ..<60:
            return "Normal"
        case 60..<75:
            return "Fair"
        case 75..<85:
            return "Serious"
        default:
            return "Critical"
        }
    }

    /// 格式化字节数
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }

    // MARK: - 数据聚合和分析

    /// 执行应用使用数据聚合
    func performAppUsageAggregation() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }

            Logger.info("开始执行应用使用数据聚合...")

            let endDate = Date()
            let startDate = Calendar.current.date(byAdding: .day, value: -7, to: endDate) ?? endDate

            // 获取聚合统计数据
            let statsResult = self.database.getAppUsageAggregatedStats(
                from: startDate,
                to: endDate
            )

            switch statsResult {
            case .success(let stats):
                Logger.info("应用使用数据聚合完成，共处理 \(stats.count) 个应用")

                // 记录前5个最常用的应用
                let topApps = Array(stats.prefix(5))
                for (index, app) in topApps.enumerated() {
                    Logger.debug("第\(index + 1)名: \(app.appName) - \(app.formattedTotalDuration)")
                }

                // 可以在这里添加更多的聚合逻辑，比如：
                // - 生成每日/每周报告
                // - 检测使用模式变化
                // - 发送通知等

            case .failure(let error):
                Logger.error("应用使用数据聚合失败: \(error)")
            }
        }
    }

    /// 获取应用使用排行榜
    func getTopApps(days: Int = 7, limit: Int = 10) -> [AppUsageStats] {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: endDate) ?? endDate

        let result = database.getAppUsageAggregatedStats(from: startDate, to: endDate)

        switch result {
        case .success(let stats):
            return Array(stats.prefix(limit))
        case .failure(let error):
            Logger.error("获取应用使用排行榜失败: \(error)")
            return []
        }
    }

    /// 获取指定应用的使用趋势
    func getAppUsageTrend(bundleID: String, days: Int = 30) -> AppUsageTrend? {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: endDate) ?? endDate

        let result = database.getAppUsageTrends(
            bundleIDs: [bundleID],
            from: startDate,
            to: endDate,
            groupBy: "day"
        )

        switch result {
        case .success(let trends):
            return trends.first
        case .failure(let error):
            Logger.error("获取应用使用趋势失败: \(error)")
            return nil
        }
    }

    // MARK: - 数据库维护

    /// 执行数据库维护任务
    func performDatabaseMaintenance() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }

            Logger.info("开始执行数据库维护任务...")

            // 1. 清理过期数据
            let cleanupResult = self.database.cleanupExpiredData(retentionDays: self.config.maxDataRetentionDays)
            switch cleanupResult {
            case .success(let deletedCount):
                Logger.info("数据清理完成，删除了 \(deletedCount) 条过期记录")
            case .failure(let error):
                Logger.error("数据清理失败: \(error)")
            }

            // 2. 优化数据库性能
            let optimizeResult = self.database.optimizeDatabasePerformance()
            switch optimizeResult {
            case .success(let report):
                Logger.info("数据库优化完成:\n\(report)")
            case .failure(let error):
                Logger.error("数据库优化失败: \(error)")
            }

            // 3. 检查数据库完整性
            let integrityResult = self.database.performIntegrityCheck()
            switch integrityResult {
            case .success(let isHealthy):
                if isHealthy {
                    Logger.success("数据库完整性检查通过")
                } else {
                    Logger.warning("数据库完整性检查发现问题，建议检查日志")
                }
            case .failure(let error):
                Logger.error("数据库完整性检查失败: \(error)")
            }

            // 4. 获取数据库大小信息
            let sizeResult = self.database.getDatabaseSizeInfo()
            switch sizeResult {
            case .success(let sizeInfo):
                Logger.info("数据库大小: \(sizeInfo.formattedTotalSize)，总记录数: \(sizeInfo.totalRecords)")
            case .failure(let error):
                Logger.error("获取数据库大小信息失败: \(error)")
            }

            Logger.success("数据库维护任务完成")
        }
    }

    /// 获取系统性能报告
    func generatePerformanceReport(days: Int = 7) -> SystemPerformanceReport? {
        let endDate = Date()
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: endDate) ?? endDate

        // 获取平均指标
        let avgMetricsResult = database.getAverageMetrics(from: startDate, to: endDate)
        guard case .success(let avgMetrics) = avgMetricsResult, let metrics = avgMetrics else {
            Logger.error("获取平均指标失败")
            return nil
        }

        // 获取峰值指标
        let peakMetricsResult = database.getPeakMetrics(from: startDate, to: endDate)
        guard case .success(let peakMetrics) = peakMetricsResult else {
            Logger.error("获取峰值指标失败")
            return nil
        }

        // 获取应用使用统计
        let appStatsResult = database.getAppUsageAggregatedStats(from: startDate, to: endDate)
        let topApps = (try? appStatsResult.get()) ?? []

        // 获取数据库大小信息
        let sizeResult = database.getDatabaseSizeInfo()
        let dbSize = (try? sizeResult.get()) ?? nil

        return SystemPerformanceReport(
            reportPeriod: "\(days)天",
            startDate: startDate,
            endDate: endDate,
            averageMetrics: metrics,
            peakCPU: peakMetrics.maxCpu,
            peakMemory: peakMetrics.maxMemory,
            peakTemperature: peakMetrics.maxTemp,
            topApps: Array(topApps.prefix(5)),
            databaseSize: dbSize,
            generatedAt: Date()
        )
    }

    /// 启动维护调度器
    private func startMaintenanceScheduler() {
        // 计算到下一个凌晨3点的时间间隔
        let now = Date()
        let calendar = Calendar.current

        var nextMaintenance = calendar.startOfDay(for: now)
        nextMaintenance = calendar.date(byAdding: .hour, value: 3, to: nextMaintenance) ?? nextMaintenance

        // 如果已经过了今天的3点，则安排到明天3点
        if nextMaintenance <= now {
            nextMaintenance = calendar.date(byAdding: .day, value: 1, to: nextMaintenance) ?? nextMaintenance
        }

        let timeInterval = nextMaintenance.timeIntervalSince(now)

        Logger.info("下次数据库维护时间: \(DateFormatter.localizedString(from: nextMaintenance, dateStyle: .short, timeStyle: .short))")

        // 设置定时器，每24小时执行一次
        maintenanceTimer = Timer.scheduledTimer(withTimeInterval: timeInterval, repeats: false) { [weak self] _ in
            self?.performDatabaseMaintenance()

            // 重新安排下一次维护（24小时后）
            self?.maintenanceTimer = Timer.scheduledTimer(withTimeInterval: 24 * 3600, repeats: true) { [weak self] _ in
                self?.performDatabaseMaintenance()
            }
        }
    }

    // MARK: - 应用切换检测优化

    /// 获取最优的应用跟踪间隔
    private func getOptimalTrackingInterval() -> TimeInterval {
        // 根据系统负载动态调整检测频率
        let cpuUsage = EnhancedSystemInfoCollector.getCPUUsage()
        let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()

        // 计算系统负载指数
        let systemLoad = (cpuUsage + memoryInfo.usagePercent) / 2.0

        switch systemLoad {
        case 0..<30:
            return 5.0 // 低负载：5秒检测一次
        case 30..<60:
            return 8.0 // 中负载：8秒检测一次
        case 60..<80:
            return 12.0 // 高负载：12秒检测一次
        default:
            return 15.0 // 超高负载：15秒检测一次
        }
    }

    /// 智能应用切换检测
    private func detectAppSwitch(currentBundleID: String, currentAppName: String) -> Bool {
        // 检查是否真的发生了应用切换
        guard lastActiveApp != currentBundleID else { return false }

        // 过滤短暂的应用切换（可能是系统对话框等）
        if let lastSwitchTime = lastAppSwitchTime {
            let timeSinceLastSwitch = Date().timeIntervalSince(lastSwitchTime)
            if timeSinceLastSwitch < 2.0 { // 2秒内的切换可能是误触
                return false
            }
        }

        // 过滤系统应用（如果配置不跟踪）
        if currentBundleID.hasPrefix("com.apple.") && !config.trackSystemApps {
            return false
        }

        // 过滤一些特殊的系统进程
        let systemProcesses = [
            "com.apple.dock",
            "com.apple.finder",
            "com.apple.systemuiserver",
            "com.apple.controlcenter"
        ]

        if systemProcesses.contains(currentBundleID) && !config.trackSystemApps {
            return false
        }

        // 记录切换时间
        lastAppSwitchTime = Date()

        Logger.debug("检测到应用切换: \(lastActiveApp ?? "无") -> \(currentBundleID)")
        return true
    }

    /// 增强的应用使用分析
    func analyzeCurrentAppUsage() -> AppUsageAnalysis? {
        guard let usage = currentAppUsage else { return nil }

        let pattern = usage.analyzeUsagePattern()
        let avgMetrics = usage.getAverageSystemMetrics()
        let peakMetrics = usage.getPeakSystemMetrics()

        return AppUsageAnalysis(
            appName: usage.appName,
            bundleID: usage.bundleID,
            duration: usage.duration,
            pattern: pattern,
            averageMetrics: avgMetrics,
            peakMetrics: peakMetrics,
            analysisTime: Date()
        )
    }

    /// 获取应用使用效率评分
    func getAppEfficiencyScore(bundleID: String) -> Double {
        guard let usage = appUsages[bundleID] else { return 0.0 }

        let pattern = usage.analyzeUsagePattern()
        let baseScore = Double(pattern.score)

        // 根据使用时长调整评分
        let durationFactor: Double
        switch usage.duration {
        case 0..<300: // 5分钟以下
            durationFactor = 0.5
        case 300..<1800: // 5-30分钟
            durationFactor = 1.0
        case 1800..<3600: // 30分钟-1小时
            durationFactor = 0.8
        default: // 超过1小时
            durationFactor = 0.6
        }

        return baseScore * durationFactor
    }

    // MARK: - 性能监控和优化总结

    /// 获取系统监控性能统计
    func getMonitoringPerformanceStats() -> MonitoringPerformanceStats {
        let stats = MonitoringPerformanceStats(
            totalDataPoints: totalDataPoints,
            lastCollectionTime: lastCollectionTime,
            isMonitoring: isMonitoring,
            databaseSize: databaseSize,
            activeAppsCount: appUsages.count,
            currentAppUsage: currentAppUsage,
            collectionInterval: config.collectionInterval,
            memoryUsage: getEstimatedMemoryUsage(),
            cpuImpact: getEstimatedCPUImpact()
        )

        return stats
    }

    /// 估算内存使用量
    private func getEstimatedMemoryUsage() -> Int {
        // 估算监控器的内存使用量（KB）
        let baseMemory = 1024 // 基础内存 1MB
        let appUsageMemory = appUsages.count * 50 // 每个应用记录约50KB
        let snapshotMemory = appUsages.values.reduce(0) { total, usage in
            total + usage.systemSnapshots.count * 2 // 每个快照约2KB
        }

        return baseMemory + appUsageMemory + snapshotMemory
    }

    /// 估算CPU影响
    private func getEstimatedCPUImpact() -> Double {
        // 估算监控器对CPU的影响（百分比）
        let baseImpact = 0.5 // 基础影响0.5%
        let collectionImpact = (1.0 / config.collectionInterval) * 0.1 // 采集频率影响
        let appTrackingImpact = config.enableAppUsageTracking ? 0.2 : 0.0

        return baseImpact + collectionImpact + appTrackingImpact
    }

    /// 优化监控性能
    func optimizeMonitoringPerformance() {
        Logger.info("开始优化监控性能...")

        // 1. 清理过期的应用使用记录
        let cutoffTime = Date().addingTimeInterval(-3600) // 1小时前
        appUsages = appUsages.filter { _, usage in
            usage.endTime == nil || usage.endTime! > cutoffTime
        }

        // 2. 限制系统快照数量
        for (bundleID, var usage) in appUsages {
            if usage.systemSnapshots.count > 50 {
                usage.systemSnapshots = Array(usage.systemSnapshots.suffix(50))
                appUsages[bundleID] = usage
            }
        }

        // 3. 根据系统负载调整采集频率
        let systemLoad = EnhancedSystemInfoCollector.getCPUUsage()
        if systemLoad > 80, config.collectionInterval < 10 {
            Logger.info("系统负载较高，调整采集间隔")
            var newConfig = config
            newConfig.collectionInterval = min(config.collectionInterval * 1.5, 15.0)
            config = newConfig
        } else if systemLoad < 30, config.collectionInterval > 5 {
            Logger.info("系统负载较低，恢复正常采集间隔")
            var newConfig = config
            newConfig.collectionInterval = max(config.collectionInterval * 0.8, 5.0)
            config = newConfig
        }

        Logger.success("监控性能优化完成")
    }

    /// 启动性能优化调度器
    private func startPerformanceOptimizationScheduler() {
        // 每小时执行一次性能优化
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            self?.optimizeMonitoringPerformance()
        }

        Logger.info("性能优化调度器已启动，每小时执行一次")
    }

    /// 记录系统信息（只记录一次）
    private func recordSystemInfoIfNeeded() {
        // 记录系统信息（如果还没有记录）
        let systemInfo = database.getSystemInfo()

        guard case .success(_?) = systemInfo else {
            // 进入这里说明：要么查询失败，要么结果是 nil
            let systemInfo = SystemInfo()
            let result = database.insertOrUpdateSystemInfo(systemInfo)
            switch result {
            case .success:
                return
            case .failure(let error):
                Logger.error("获取系统信息失败: \(error)")
            }
            return
        }
    }

    // MARK: - 应用使用跟踪

    /// 开始应用使用跟踪
    private func startAppUsageTracking() {
//        guard hasAccessibilityPermission else {
//            Logger.warning("没有辅助功能权限，无法跟踪应用使用")
//            return
//        }

        // 优化检测频率：根据系统负载动态调整
        let interval = config.enableAppUsageTracking ? getOptimalTrackingInterval() : 10.0
        appUsageTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            self?.trackActiveApp()
        }

        // 立即检查一次
        trackActiveApp()
    }

    /// 跟踪当前活跃应用
    private func trackActiveApp() {
        // 使用专用队列处理应用跟踪，避免阻塞主线程
        appUsageQueue.async { [weak self] in
            guard let self = self else { return }

            let activeApp = EnhancedSystemInfoCollector.getActiveApplication()

            guard let bundleID = activeApp.bundleID,
                  let appName = activeApp.name
            else {
                return
            }

            // 使用智能检测机制判断是否发生了真正的应用切换
            if self.detectAppSwitch(currentBundleID: bundleID, currentAppName: appName) {
                // 结束之前的应用使用记录
                self.endCurrentAppUsage()

                // 开始新的应用使用记录
                self.startNewAppUsage(bundleID: bundleID, appName: appName)

                self.lockQueue.sync {
                    self.lastActiveApp = bundleID
                }

                Logger.debug("应用切换: \(appName) (\(bundleID))")
            } else {
                // 更新当前应用的使用数据
                self.updateCurrentAppUsage()
            }

            self.lockQueue.sync {
                self.saveCount += 1
            }
        }
    }

    /// 开始新的应用使用记录
    private func startNewAppUsage(bundleID: String, appName: String) {
        // 检查是否已有该应用的记录
        if let existingUsage = appUsages[bundleID] {
            // 如果存在但未结束，先结束之前的会话
            if existingUsage.endTime == nil {
                var usage = existingUsage
                usage.endSession()
                appUsages[bundleID] = usage

                if usage.duration > 30 {
                    saveAppUsageToDatabase(usage)
                }
            }
        }

        // 创建新的使用记录
        currentAppUsage = AppUsageRecord(bundleID: bundleID, appName: appName)
        appUsages[bundleID] = currentAppUsage

        Logger.debug("开始跟踪应用: \(appName) (\(bundleID))")
    }

    /// 更新当前应用使用数据
    private func updateCurrentAppUsage() {
        guard var usage = currentAppUsage else { return }

        // 获取详细的系统指标
        let cpuMetrics = EnhancedSystemInfoCollector.getDetailedCPUMetrics()
        let memoryMetrics = EnhancedSystemInfoCollector.getDetailedMemoryMetrics()
        let diskMetrics = EnhancedSystemInfoCollector.getDetailedDiskMetrics()
        let networkMetrics = EnhancedSystemInfoCollector.getDetailedNetworkMetrics()

        // 更新CPU使用数据
        usage.updateCPUUsage(cpuMetrics.usagePercent)

        // 更新内存使用数据（转换为MB）
        usage.updateMemoryUsage(Double(memoryMetrics.usedMB))

        // 更新持续时间
        usage.duration = usage.getDurationNoRecord()

        // 创建应用关联的系统指标快照
        let systemSnapshot = AppSystemSnapshot(
            timestamp: Date(),
            cpuUsage: cpuMetrics.usagePercent,
            memoryUsageMB: memoryMetrics.usedMB,
            diskUsage: diskMetrics.usagePercent,
            networkBytesIn: networkMetrics.bytesIn,
            networkBytesOut: networkMetrics.bytesOut,
            loadAvg1min: cpuMetrics.loadAvg1min,
            memoryPressure: memoryMetrics.memoryPressure
        )

        // 将系统快照关联到应用使用记录
        usage.addSystemSnapshot(systemSnapshot)

        // 保存更新后的记录
        currentAppUsage = usage
        appUsages[usage.bundleID] = usage

        Logger.debug("更新跟踪应用: \(usage.appName)，当前使用时长: \(String(format: "%.1f", usage.duration))秒，CPU: \(String(format: "%.1f", cpuMetrics.usagePercent))%")
    }

    /// 结束当前应用使用记录
    private func endCurrentAppUsage() {
        guard var usage = currentAppUsage else { return }

        usage.endSession()

        // 更新内存中的记录
        appUsages[usage.bundleID] = usage

        // 只记录使用时间超过30秒的应用
        if usage.duration > 30 {
            saveAppUsageToDatabase(usage)
        } else {
            Logger.debug("应用使用时间过短，不记录: \(usage.appName) (\(String(format: "%.1f", usage.duration))秒)")
        }

        currentAppUsage = nil
        Logger.debug("结束跟踪应用: \(usage.appName)，使用时长: \(String(format: "%.1f", usage.duration))秒")
    }

    /// 保存应用使用记录到数据库
    private func saveAppUsageToDatabase(_ usage: AppUsageRecord) {
        // 使用专用队列处理数据库操作，避免阻塞其他操作
        dataCollectionQueue.async { [weak self] in
            guard let self = self else { return }

            // 首先确保应用信息存在于数据库中
            let appInfoResult = self.database.getOrCreateAppInfo(
                bundleID: usage.bundleID,
                appName: usage.appName
            )

            switch appInfoResult {
            case .success(let appInfo):
                // 创建应用使用会话记录
                let session = AppUsageSession(
                    appID: appInfo.id,
                    sessionStart: usage.startTime
                )

                var sessionToSave = session
                sessionToSave.sessionEnd = usage.endTime
                sessionToSave.durationSeconds = Int(usage.duration)
                sessionToSave.peakCPUUsage = usage.peakCPUUsage
                sessionToSave.avgCPUUsage = usage.avgCPUUsage
                sessionToSave.peakMemoryMB = Int(usage.peakMemoryUsageMB)
                sessionToSave.avgMemoryMB = Int(usage.avgMemoryUsageMB)
                sessionToSave.isActive = false
                sessionToSave.updatedAt = Date()

                // 保存会话记录
                let sessionResult = self.database.insertAppUsageSession(sessionToSave)

                switch sessionResult {
                case .success(let sessionID):
                    Logger.debug("应用使用会话保存成功: \(usage.appName)")

                    // 保存详细的CPU和内存使用数据（可选，用于详细分析）
                    if !usage.CPUUsages.isEmpty, usage.CPUUsages.count == usage.memoryUsageMBs.count {
                        self.saveAppUsageDetails(sessionID: sessionID, usage: usage)
                    }

                case .failure(let error):
                    Logger.error("保存应用使用会话失败: \(error)")
                }

            case .failure(let error):
                Logger.error("获取或创建应用信息失败: \(error)")
            }
        }
    }

    /// 保存应用使用详细数据
    private func saveAppUsageDetails(sessionID: Int64, usage: AppUsageRecord) {
        let startTime = usage.startTime
        let interval = usage.duration / Double(usage.CPUUsages.count)

        var details: [AppUsageDetail] = []

        for (index, cpuUsage) in usage.CPUUsages.enumerated() {
            if index < usage.memoryUsageMBs.count {
                let timestamp = startTime.addingTimeInterval(interval * Double(index))
                let memoryUsage = usage.memoryUsageMBs[index]

                let detail = AppUsageDetail(
                    sessionID: sessionID,
                    timestamp: timestamp,
                    cpuUsage: cpuUsage,
                    memoryUsageMB: Int(memoryUsage)
                )

                details.append(detail)
            }
        }

        // 批量插入详细数据
        if !details.isEmpty {
            let result = database.insertAppUsageDetails(details)
            switch result {
            case .success():
                Logger.debug("应用使用详细数据保存成功，共 \(details.count) 条记录")
            case .failure(let error):
                Logger.error("保存应用使用详细数据失败: \(error)")
            }
        }
    }

    // MARK: - 通知处理

    private func setupNotifications() {
        // 监听某个应用失去激活状态（退到后台）
        NSWorkspace.shared.notificationCenter.addObserver(
            forName: NSWorkspace.didDeactivateApplicationNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.debug("应用使用didDeactivateApplicationNotification")
        }

        // 监听应用切换事件
        NSWorkspace.shared.notificationCenter.addObserver(
            forName: NSWorkspace.didActivateApplicationNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.debug("应用使用记录保存成功: \(String(describing: self.currentAppUsage?.appName)), 使用时长: \(Int(self.currentAppUsage?.getDurationNoRecord() ?? 0.0))秒")
        }
    }

    // MARK: - 状态更新

    private func updateStatus() {
        databaseSize = database.getDatabaseSize()
    }

    // MARK: - 数据管理

    /// 清除所有数据
    func clearAllData() -> Bool {
        Logger.info("开始清除所有监控数据...")

        // 停止监控以避免在清理过程中产生新数据
        let wasMonitoring = isMonitoring
        if wasMonitoring {
            stopMonitoring()
        }

        // 清除数据库中的所有数据
        let result = database.clearAllData()

        switch result {
        case .success(let success):
            if success {
                // 重置内存中的状态
                lockQueue.sync {
                    self._totalDataPoints = 0
                    self._lastCollectionTime = nil
                }

                // 清除应用使用记录
                appUsages.removeAll()
                currentAppUsage = nil
                lastActiveApp = nil

                // 更新状态
                databaseSize = 0
                saveCount = 0
                updateStatus()

                Logger.success("所有监控数据已清除")

                // 如果之前在监控，重新开始监控
                if wasMonitoring {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self.startMonitoring()
                    }
                }

                return true
            } else {
                Logger.error("清除数据失败")
                return false
            }

        case .failure(let error):
            Logger.error("清除数据时发生错误: \(error)")

            // 如果之前在监控，重新开始监控
            if wasMonitoring {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.startMonitoring()
                }
            }

            return false
        }
    }

    /// 清除旧数据
    func cleanupOldData() -> Bool {
        let totalDeleted = (try? database.cleanupOldData(olderThanDays: config.maxDataRetentionDays).get()) ?? -1
        if totalDeleted > 0 {
            updateStatus()
            Logger.info("已清除\(config.maxDataRetentionDays)天前的旧数据")
        }
        return totalDeleted > 0
    }

    /// 获取数据统计
    func getDataStats() -> [String: Any] {
        return [
            "total_data_points": totalDataPoints,
            "database_size_mb": Double(databaseSize) / (1024 * 1024),
            "last_collection": lastCollectionTime?.timeIntervalSince1970 ?? 0,
            "is_monitoring": isMonitoring,
            "collection_interval": config.collectionInterval,
            "has_accessibility_permission": hasAccessibilityPermission
        ]
    }

    /// 获取最近的指标数据（用于图表显示）
    func getRecentMetrics(days: Int = 7) -> [[String: Any]] {
        let endDate = Date()
        let startDate = endDate.addingTimeInterval(-TimeInterval(days * 24 * 3600))

        Logger.debug("获取最近\(days)天的指标数据，时间范围: \(startDate) - \(endDate)")

        var metricsData: [[String: Any]] = []

        // 获取CPU指标数据
        let cpuResult = database.getCPUMetrics(from: startDate, to: endDate, limit: 1000)
        if case .success(let cpuMetrics) = cpuResult {
            for metric in cpuMetrics {
                let dataPoint: [String: Any] = [
                    "timestamp": metric.timestamp.timeIntervalSince1970,
                    "date": ISO8601DateFormatter().string(from: metric.timestamp),
                    "type": "cpu",
                    "usage_percent": metric.usagePercent,
                    "user_percent": metric.userPercent,
                    "system_percent": metric.systemPercent,
                    "idle_percent": metric.idlePercent,
                    "load_avg_1min": metric.loadAvg1min ?? 0,
                    "load_avg_5min": metric.loadAvg5min ?? 0,
                    "load_avg_15min": metric.loadAvg15min ?? 0,
                    "core_count": metric.coreCount,
                    "frequency_mhz": metric.frequencyMHz ?? 0
                ]
                metricsData.append(dataPoint)
            }
        }

        // 获取内存指标数据
        let memoryResult = database.getMemoryMetrics(from: startDate, to: endDate, limit: 1000)
        if case .success(let memoryMetrics) = memoryResult {
            for metric in memoryMetrics {
                let dataPoint: [String: Any] = [
                    "timestamp": metric.timestamp.timeIntervalSince1970,
                    "date": ISO8601DateFormatter().string(from: metric.timestamp),
                    "type": "memory",
                    "usage_percent": metric.usagePercent,
                    "used_mb": metric.usedMB,
                    "total_mb": metric.totalMB,
                    "available_mb": metric.availableMB,
                    "active_mb": metric.activeMB,
                    "inactive_mb": metric.inactiveMB,
                    "wired_mb": metric.wiredMB,
                    "compressed_mb": metric.compressedMB,
                    "swap_used_mb": metric.swapUsedMB,
                    "swap_total_mb": metric.swapTotalMB,
                    "memory_pressure": metric.memoryPressure
                ]
                metricsData.append(dataPoint)
            }
        }

        // 获取时间序列数据（用于图表显示）
        let timeSeriesTypes = ["cpu", "memory", "battery"]
        for metricType in timeSeriesTypes {
            let timeSeriesResult = database.getTimeSeriesData(
                metricType: metricType,
                from: startDate,
                to: endDate,
                interval: 3600 // 1小时间隔
            )

            if case .success(let timeSeriesData) = timeSeriesResult {
                for dataPoint in timeSeriesData {
                    let chartDataPoint: [String: Any] = [
                        "timestamp": dataPoint.timestamp.timeIntervalSince1970,
                        "date": ISO8601DateFormatter().string(from: dataPoint.timestamp),
                        "type": "\(metricType)_timeseries",
                        "value": dataPoint.value,
                        "metric_type": metricType
                    ]
                    metricsData.append(chartDataPoint)
                }
            }
        }

        // 按时间戳排序
        metricsData.sort {
            guard let timestamp1 = $0["timestamp"] as? TimeInterval,
                  let timestamp2 = $1["timestamp"] as? TimeInterval else {
                return false
            }
            return timestamp1 < timestamp2
        }

        Logger.info("获取到 \(metricsData.count) 个指标数据点")
        return metricsData
    }

    /// 获取应用使用汇总
    func getAppUsageSummary(days: Int = 7) -> [[String: Any]] {
        let endDate = Date()
        let startDate = endDate.addingTimeInterval(-TimeInterval(days * 24 * 3600))

        Logger.debug("获取最近\(days)天的应用使用汇总数据")

        var summaryData: [[String: Any]] = []

        // 获取应用使用聚合统计数据
        let statsResult = database.getAppUsageAggregatedStats(from: startDate, to: endDate)

        switch statsResult {
        case .success(let appStats):
            for stat in appStats {
                let appSummary: [String: Any] = [
                    "bundle_id": stat.bundleID,
                    "app_name": stat.appName,
                    "app_category": stat.appCategory ?? "未知",
                    "session_count": stat.sessionCount,
                    "total_duration_seconds": stat.totalDurationSeconds,
                    "total_duration_formatted": stat.formattedTotalDuration,
                    "avg_duration_seconds": stat.avgDurationSeconds,
                    "avg_duration_formatted": stat.formattedAvgDuration,
                    "max_cpu_usage": stat.maxCPUUsage,
                    "avg_cpu_usage": stat.avgCPUUsage,
                    "max_memory_mb": stat.maxMemoryMB,
                    "avg_memory_mb": stat.avgMemoryMB,
                    "first_used": stat.firstUsed.timeIntervalSince1970,
                    "first_used_formatted": DateFormatter.localizedString(from: stat.firstUsed, dateStyle: .short, timeStyle: .short),
                    "last_used": stat.lastUsed.timeIntervalSince1970,
                    "last_used_formatted": DateFormatter.localizedString(from: stat.lastUsed, dateStyle: .short, timeStyle: .short),
                    "usage_frequency": calculateUsageFrequency(sessionCount: stat.sessionCount, days: days),
                    "efficiency_score": calculateEfficiencyScore(stat: stat)
                ]
                summaryData.append(appSummary)
            }

        case .failure(let error):
            Logger.error("获取应用使用统计失败: \(error)")
        }

        // 获取应用使用趋势数据
        let trendsResult = database.getAppUsageTrends(from: startDate, to: endDate, groupBy: "day")

        switch trendsResult {
        case .success(let trends):
            for trend in trends {
                // 查找对应的统计数据并添加趋势信息
                if let index = summaryData.firstIndex(where: {
                    ($0["bundle_id"] as? String) == trend.bundleID
                }) {
                    summaryData[index]["trend_data"] = [
                        "total_duration": trend.totalDuration,
                        "active_days": trend.activeDays,
                        "avg_daily_duration": trend.avgDailyDuration,
                        "trend_direction": trend.trend,
                        "daily_data": trend.dailyData.map { daily in
                            return [
                                "date": daily.date,
                                "duration": daily.duration,
                                "sessions": daily.sessions,
                                "avg_cpu": daily.avgCPU,
                                "avg_memory": daily.avgMemory
                            ]
                        }
                    ]
                }
            }

        case .failure(let error):
            Logger.error("获取应用使用趋势失败: \(error)")
        }

        // 按总使用时长排序
        summaryData.sort {
            guard let duration1 = $0["total_duration_seconds"] as? Int,
                  let duration2 = $1["total_duration_seconds"] as? Int else {
                return false
            }
            return duration1 > duration2
        }

        Logger.info("获取到 \(summaryData.count) 个应用的使用汇总数据")
        return summaryData
    }

    /// 计算使用频率
    private func calculateUsageFrequency(sessionCount: Int, days: Int) -> String {
        let avgSessionsPerDay = Double(sessionCount) / Double(days)

        switch avgSessionsPerDay {
        case 0..<0.5:
            return "很少使用"
        case 0.5..<2.0:
            return "偶尔使用"
        case 2.0..<5.0:
            return "经常使用"
        case 5.0..<10.0:
            return "频繁使用"
        default:
            return "重度使用"
        }
    }

    /// 计算效率评分
    private func calculateEfficiencyScore(stat: AppUsageStats) -> Double {
        // 基于使用时长、CPU使用率、内存使用量等计算效率评分
        let durationScore = min(Double(stat.totalDurationSeconds) / 3600.0, 10.0) // 最高10分
        let cpuScore = max(0, 10.0 - stat.avgCPUUsage / 10.0) // CPU使用率越低分数越高
        let memoryScore = max(0, 10.0 - Double(stat.avgMemoryMB) / 100.0) // 内存使用量越低分数越高
        let sessionScore = min(Double(stat.sessionCount) / 10.0, 10.0) // 会话数适中得分高

        return (durationScore + cpuScore + memoryScore + sessionScore) / 4.0 * 10.0 // 转换为0-100分
    }
}
