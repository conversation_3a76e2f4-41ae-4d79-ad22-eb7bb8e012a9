//
//  EnhancedSystemMonitorModels.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import Foundation

// MARK: - 系统信息模型

struct SystemInfo: Codable {
    let id: String
    let deviceModel: String
    let cpuName: String
    let cpuFrequency: Int?
    let cpuCoreCount: Int
    let totalMemoryGB: Double
    let totalDiskGB: Double
    let macOSVersion: String
    let systemBootTime: Date
    let recordedAt: Date
    let kernelVersion: String // 内核版本
    let systemSerial: String // 序列号
    let cpuArchitecture: String // cpu架构
    
    init() {
        self.id = UUID().uuidString
        self.deviceModel = EnhancedSystemInfoCollector.getDeviceModel()
        self.cpuName = EnhancedSystemInfoCollector.getCPUName()
        self.cpuCoreCount = EnhancedSystemInfoCollector.getCPUCoreCount()
        self.cpuArchitecture = EnhancedSystemInfoCollector.getCPUArchitecture()
        self.totalMemoryGB = EnhancedSystemInfoCollector.getMemoryInfo().totalGB
        self.totalDiskGB = EnhancedSystemInfoCollector.getDiskInfo().totalGB
        self.cpuFrequency = EnhancedSystemInfoCollector.getCPUFrequency()
        self.macOSVersion = EnhancedSystemInfoCollector.getMacOSVersion()
        self.kernelVersion = EnhancedSystemInfoCollector.getKernelVersion()
        self.systemSerial = EnhancedSystemInfoCollector.getSystemSerial()
        self.systemBootTime = EnhancedSystemInfoCollector.getSystemBootTime()
        self.recordedAt = Date()
    }

    init(deviceModel: String, cpuName: String, totalMemoryGB: Double, totalDiskGB: Double, macOSVersion: String, recordedAt: Date) {
        self.id = UUID().uuidString
        self.deviceModel = deviceModel
        self.cpuName = cpuName
        self.totalMemoryGB = totalMemoryGB
        self.totalDiskGB = totalDiskGB
        self.macOSVersion = macOSVersion
        self.systemBootTime = EnhancedSystemInfoCollector.getSystemBootTime()
        self.cpuCoreCount = EnhancedSystemInfoCollector.getCPUCoreCount()
        self.cpuArchitecture = EnhancedSystemInfoCollector.getCPUArchitecture()
        self.cpuFrequency = EnhancedSystemInfoCollector.getCPUFrequency()
        self.kernelVersion = EnhancedSystemInfoCollector.getKernelVersion()
        self.systemSerial = EnhancedSystemInfoCollector.getSystemSerial()
        self.recordedAt = recordedAt
    }
    
    
    /// 打印系统信息
    func printInfo() {
        print("""
        ===== System Info =====
        ID: \(id)
        Device Model: \(deviceModel)
        CPU Name: \(cpuName)
        CPU Architecture: \(cpuArchitecture)
        CPU Frequency: \(cpuFrequency != nil ? "\(cpuFrequency!) MHz" : "Unknown")
        CPU Core Count: \(cpuCoreCount)
        Total Memory: \(String(format: "%.2f GB", totalMemoryGB))
        Total Disk: \(String(format: "%.2f GB", totalDiskGB))
        macOS Version: \(macOSVersion)
        Kernel Version: \(kernelVersion)
        System Serial: \(systemSerial)
        System Boot Time: \(format(date: systemBootTime))
        Recorded At: \(format(date: recordedAt))
        =======================
        """)
    }

    /// 日期格式化
    private func format(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.string(from: date)
    }
}

// MARK: - 增强版系统指标模型

/// 增强版系统指标，包含更详细的信息
struct EnhancedSystemMetrics: Codable, Identifiable {
    let id: String
    let timestamp: Date
    
    // CPU相关
    let cpuUsagePercent: Double
    let cpuTemperature: Double?
    let cpuCoreCount: Int
    
    // 内存相关
    let memoryUsagePercent: Double
    let memoryUsedGB: Double
    let memoryAvailableGB: Double
    let memoryTotalGB: Double
    let swapUsedGB: Double
    let swapTotalGB: Double
    
    // 磁盘相关
    let diskFreeGB: Double
    let diskUsagePercent: Double
    let diskTotalGB: Double
    
    // 电池相关（可选，桌面机可能没有）
    let batteryLevel: Int?
    let batteryHealth: Double?
    let batteryCycleCount: Int?
    let powerSourceType: String
    
    // 温度和风扇
    let gpuTemperature: Double?
    let fanSpeed: Int?
    
    // 当前活跃应用
    let activeAppBundleID: String?
    let activeAppName: String?
    
    // 系统负载
    let loadAverage1min: Double
    let loadAverage5min: Double
    let loadAverage15min: Double
    
    // 网络相关
    let networkBytesIn: UInt64
    let networkBytesOut: UInt64
    
    // 进程信息
    let topProcesses: [ProcessInfoModel]
    
    init() {
        self.id = UUID().uuidString
        self.timestamp = Date()
        
        // 采集CPU信息
        self.cpuUsagePercent = EnhancedSystemInfoCollector.getCPUUsage()
        self.cpuTemperature = EnhancedSystemInfoCollector.getCPUTemperature()
        self.cpuCoreCount = EnhancedSystemInfoCollector.getCPUCoreCount()
        
        // 采集内存信息
        let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()
        self.memoryUsagePercent = memoryInfo.usagePercent
        self.memoryUsedGB = memoryInfo.usedGB
        self.memoryAvailableGB = memoryInfo.availableGB
        self.memoryTotalGB = memoryInfo.totalGB
        self.swapUsedGB = memoryInfo.swapUsedGB
        self.swapTotalGB = memoryInfo.swapTotalGB
        
        // 采集磁盘信息
        let diskInfo = EnhancedSystemInfoCollector.getDiskInfo()
        self.diskFreeGB = diskInfo.freeGB
        self.diskUsagePercent = diskInfo.usagePercent
        self.diskTotalGB = diskInfo.totalGB
        
        // 采集电池信息
        let batteryInfo = EnhancedSystemInfoCollector.getBatteryInfo()
        self.batteryLevel = batteryInfo.level
        self.batteryHealth = batteryInfo.health
        self.batteryCycleCount = batteryInfo.cycleCount
        self.powerSourceType = batteryInfo.powerSourceType
        
        // 采集温度和风扇信息
        self.gpuTemperature = EnhancedSystemInfoCollector.getGPUTemperature()
        self.fanSpeed = EnhancedSystemInfoCollector.getFanSpeed()
        
        // 采集当前活跃应用
        let activeApp = EnhancedSystemInfoCollector.getActiveApplication()
        self.activeAppBundleID = activeApp.bundleID
        self.activeAppName = activeApp.name
        
        // 采集系统负载
        let loadAvg = EnhancedSystemInfoCollector.getLoadAverage()
        self.loadAverage1min = loadAvg.oneMin
        self.loadAverage5min = loadAvg.fiveMin
        self.loadAverage15min = loadAvg.fifteenMin
        
        // 采集网络信息
        let networkInfo = EnhancedSystemInfoCollector.getNetworkInfo()
        self.networkBytesIn = networkInfo.bytesIn
        self.networkBytesOut = networkInfo.bytesOut
        
        // 采集进程信息
        self.topProcesses = EnhancedSystemInfoCollector.getTopProcesses()
    }
}

// MARK: - 应用使用记录模型

/// 应用使用记录
struct AppUsageRecord: Codable, Identifiable {
    let id: String
    let bundleID: String
    let appName: String
    var startTime: Date
    var endTime: Date?
    var duration: TimeInterval
    var peakCPUUsage: Double
    var CPUUsages: [Double]
    var peakMemoryUsageMB: Double
    var memoryUsageMBs: [Double]
    var avgCPUUsage: Double
    var avgMemoryUsageMB: Double

    // 系统快照数据
    var systemSnapshots: [AppSystemSnapshot] = []
    
    init(bundleID: String, appName: String) {
        self.id = UUID().uuidString
        self.bundleID = bundleID
        self.appName = appName
        self.startTime = Date()
        self.endTime = nil
        self.duration = 0
        self.peakCPUUsage = 0
        self.CPUUsages = []
        self.peakMemoryUsageMB = 0
        self.memoryUsageMBs = []
        self.avgCPUUsage = 0
        self.avgMemoryUsageMB = 0
        self.systemSnapshots = []
    }
    
    /// 结束应用使用记录
    mutating func endSession() {
        self.endTime = Date()
        if let endTime = self.endTime {
            let du = endTime.timeIntervalSince(self.startTime)
            self.duration += du
        }
    }
    
    /// 结束应用使用记录
    func getDurationNoRecord() -> TimeInterval {
        let time = Date()
        return time.timeIntervalSince(self.startTime)
    }
    
    /// 结束应用使用记录
    mutating func resetStartTime() {
        self.startTime = Date()
    }
    
    /// 更新CPU使用率
    mutating func updateCPUUsage(_ usage: Double) {
        self.peakCPUUsage = max(self.peakCPUUsage, usage)
        self.CPUUsages.append(usage)
        // 简单的移动平均
        self.avgCPUUsage = (self.avgCPUUsage + usage) / 2.0
    }
    
    /// 更新内存使用量
    mutating func updateMemoryUsage(_ usageMB: Double) {
        self.peakMemoryUsageMB = max(self.peakMemoryUsageMB, usageMB)
        self.memoryUsageMBs.append(usageMB)
        // 简单的移动平均
        self.avgMemoryUsageMB = (self.avgMemoryUsageMB + usageMB) / 2.0
    }

    /// 添加系统快照
    mutating func addSystemSnapshot(_ snapshot: AppSystemSnapshot) {
        systemSnapshots.append(snapshot)

        // 限制快照数量，避免内存过度使用
        if systemSnapshots.count > 100 {
            systemSnapshots.removeFirst(systemSnapshots.count - 100)
        }
    }

    /// 获取平均系统指标
    func getAverageSystemMetrics() -> (avgCPU: Double, avgMemory: Double, avgDisk: Double, avgNetworkActivity: Double)? {
        guard !systemSnapshots.isEmpty else { return nil }

        let totalCPU = systemSnapshots.reduce(0.0) { $0 + $1.cpuUsage }
        let totalMemory = systemSnapshots.reduce(0.0) { $0 + Double($1.memoryUsageMB) }
        let totalDisk = systemSnapshots.reduce(0.0) { $0 + $1.diskUsage }
        let totalNetwork = systemSnapshots.reduce(0.0) { $0 + Double($1.networkBytesIn + $1.networkBytesOut) }

        let count = Double(systemSnapshots.count)

        return (
            avgCPU: totalCPU / count,
            avgMemory: totalMemory / count,
            avgDisk: totalDisk / count,
            avgNetworkActivity: totalNetwork / count
        )
    }

    /// 获取峰值系统指标
    func getPeakSystemMetrics() -> (peakCPU: Double, peakMemory: Int, peakDisk: Double, peakNetworkActivity: Int64)? {
        guard !systemSnapshots.isEmpty else { return nil }

        let peakCPU = systemSnapshots.max(by: { $0.cpuUsage < $1.cpuUsage })?.cpuUsage ?? 0
        let peakMemory = systemSnapshots.max(by: { $0.memoryUsageMB < $1.memoryUsageMB })?.memoryUsageMB ?? 0
        let peakDisk = systemSnapshots.max(by: { $0.diskUsage < $1.diskUsage })?.diskUsage ?? 0
        let peakNetwork = systemSnapshots.max(by: {
            ($0.networkBytesIn + $0.networkBytesOut) < ($1.networkBytesIn + $1.networkBytesOut)
        }).map { $0.networkBytesIn + $0.networkBytesOut } ?? 0

        return (
            peakCPU: peakCPU,
            peakMemory: peakMemory,
            peakDisk: peakDisk,
            peakNetworkActivity: peakNetwork
        )
    }

    /// 分析应用使用模式
    func analyzeUsagePattern() -> AppUsagePattern {
        guard !systemSnapshots.isEmpty else {
            return AppUsagePattern(
                pattern: "数据不足",
                intensity: "未知",
                resourceUsage: "未知",
                recommendations: ["需要更多使用数据进行分析"]
            )
        }

        let avgMetrics = getAverageSystemMetrics()!
        let peakMetrics = getPeakSystemMetrics()!

        // 分析使用强度
        let intensity: String
        if avgMetrics.avgCPU > 70 || avgMetrics.avgMemory > 2048 {
            intensity = "高强度"
        } else if avgMetrics.avgCPU > 30 || avgMetrics.avgMemory > 1024 {
            intensity = "中强度"
        } else {
            intensity = "低强度"
        }

        // 分析资源使用特征
        let resourceUsage: String
        if avgMetrics.avgCPU > avgMetrics.avgMemory / 50 {
            resourceUsage = "CPU密集型"
        } else if avgMetrics.avgMemory > 1024 {
            resourceUsage = "内存密集型"
        } else if avgMetrics.avgNetworkActivity > 1024*1024 {
            resourceUsage = "网络密集型"
        } else {
            resourceUsage = "轻量级"
        }

        // 生成建议
        var recommendations: [String] = []
        if peakMetrics.peakCPU > 90 {
            recommendations.append("应用CPU使用率过高，建议检查是否有性能问题")
        }
        if peakMetrics.peakMemory > 4096 {
            recommendations.append("应用内存使用量较大，建议关注内存泄漏")
        }
        if duration > 3600 { // 超过1小时
            recommendations.append("长时间使用，建议适当休息")
        }

        return AppUsagePattern(
            pattern: "\(intensity) + \(resourceUsage)",
            intensity: intensity,
            resourceUsage: resourceUsage,
            recommendations: recommendations.isEmpty ? ["使用正常"] : recommendations
        )
    }
}

// MARK: - 辅助数据结构

/// 内存信息
struct MemoryInfo {
    let usagePercent: Double
    let usedGB: Double
    let availableGB: Double
    let totalGB: Double
    let swapUsedGB: Double
    let swapTotalGB: Double
}

/// 磁盘信息
struct DiskInfo {
    let freeGB: Double
    let usagePercent: Double
    let totalGB: Double
}

/// 电池信息
struct BatteryInfo {
    let level: Int?
    let health: Double?
    let cycleCount: Int?
    let powerSourceType: String
}

/// 活跃应用信息
struct ActiveAppInfo {
    let bundleID: String?
    let name: String?
}

/// 系统负载信息
struct LoadAverageInfo {
    let oneMin: Double
    let fiveMin: Double
    let fifteenMin: Double
}

/// 网络信息
struct NetworkInfo {
    let bytesIn: UInt64
    let bytesOut: UInt64
}

/// 进程信息模型（增强版）
struct ProcessInfoModel: Codable, Identifiable {
    let id = UUID()
    let pid: Int32
    let name: String
    let cpuUsage: Double
    let memoryUsageMB: Double
    let bundleID: String?
    
    enum CodingKeys: String, CodingKey {
        case pid, name, cpuUsage, memoryUsageMB, bundleID
    }
}

// MARK: - 监控配置

/// 监控配置
struct MonitorConfig: Decodable, Encodable {
    var collectionInterval: TimeInterval = 60 // 默认1分钟
    var enableBatteryMonitoring: Bool = true
    var enableTemperatureMonitoring: Bool = true
    var enableAppUsageTracking: Bool = true
    var maxDataRetentionDays: Int = 30
    var enableDetailedProcessInfo: Bool = true
    var trackSystemApps: Bool = false
    
    enum CollectionInterval: TimeInterval, CaseIterable {
        case oneMinute = 60
        case fiveMinutes = 300
        case tenMinutes = 600
        case thirtyMinutes = 1800
        case oneHour = 3600
        
        var displayName: String {
            switch self {
            case .oneMinute: return "1分钟"
            case .fiveMinutes: return "5分钟"
            case .tenMinutes: return "10分钟"
            case .thirtyMinutes: return "30分钟"
            case .oneHour: return "1小时"
            }
        }
    }
}

// MARK: - 数据分析结果

/// 系统健康评估结果
struct SystemHealthAssessment {
    let overallScore: Double // 0-100分
    let cpuHealthScore: Double
    let memoryHealthScore: Double
    let diskHealthScore: Double
    let batteryHealthScore: Double?
    let temperatureHealthScore: Double
    
    let recommendations: [String]
    let warnings: [String]
    let upgradeRecommendations: [String]
    
    let assessmentDate: Date
}

/// AI分析用的数据摘要
struct SystemAnalysisSummary: Codable {
    let deviceModel: String
    let cpuName: String
    let totalMemoryGB: Double
    let totalDiskGB: Double
    let macOSVersion: String
    
    // 最近7天的平均值
    let avgCPUUsage: Double
    let avgMemoryUsage: Double
    let avgDiskUsage: Double
    let avgTemperature: Double?
    
    // 峰值数据
    let peakCPUUsage: Double
    let peakMemoryUsage: Double
    let peakTemperature: Double?
    
    // 电池健康（如果有）
    let batteryHealth: Double?
    let batteryCycleCount: Int?
    
    // 应用使用模式
    let topApps: [String] // 最常用的应用
    let totalUsageHours: Double // 总使用时长
    
    // 性能趋势
    let performanceTrend: String // "improving", "stable", "declining"
    
    let analysisDate: Date
}

// MARK: - 应用关联系统指标

/// 应用系统快照模型
struct AppSystemSnapshot: Codable {
    let timestamp: Date
    let cpuUsage: Double            // CPU使用率 (0-100)
    let memoryUsageMB: Int          // 内存使用量 (MB)
    let diskUsage: Double           // 磁盘使用率 (0-100)
    let networkBytesIn: Int64       // 网络入站字节
    let networkBytesOut: Int64      // 网络出站字节
    let loadAvg1min: Double?        // 1分钟负载平均值
    let memoryPressure: Int         // 内存压力 (0-100)

    /// 计算网络活动强度
    var networkActivity: String {
        let totalBytes = networkBytesIn + networkBytesOut
        switch totalBytes {
        case 0..<1024*1024:         // < 1MB
            return "低"
        case 1024*1024..<10*1024*1024:  // 1-10MB
            return "中"
        default:                    // > 10MB
            return "高"
        }
    }

    /// 系统负载状态
    var systemLoadStatus: String {
        guard let load = loadAvg1min else { return "未知" }

        switch load {
        case 0..<1.0:
            return "轻载"
        case 1.0..<2.0:
            return "中载"
        case 2.0..<4.0:
            return "重载"
        default:
            return "超载"
        }
    }
}

/// 应用使用模式分析结果
struct AppUsagePattern: Codable {
    let pattern: String             // 使用模式描述
    let intensity: String           // 使用强度：高强度、中强度、低强度
    let resourceUsage: String       // 资源使用特征：CPU密集型、内存密集型、网络密集型、轻量级
    let recommendations: [String]   // 使用建议

    /// 模式评分 (0-100)
    var score: Int {
        switch intensity {
        case "高强度":
            return 80
        case "中强度":
            return 60
        case "低强度":
            return 40
        default:
            return 0
        }
    }

    /// 是否需要关注
    var needsAttention: Bool {
        return recommendations.contains { $0.contains("过高") || $0.contains("较大") || $0.contains("问题") }
    }
}

/// 应用使用分析结果
struct AppUsageAnalysis: Codable {
    let appName: String
    let bundleID: String
    let duration: TimeInterval
    let pattern: AppUsagePattern
    let averageMetrics: (avgCPU: Double, avgMemory: Double, avgDisk: Double, avgNetworkActivity: Double)?
    let peakMetrics: (peakCPU: Double, peakMemory: Int, peakDisk: Double, peakNetworkActivity: Int64)?
    let analysisTime: Date

    /// 效率评分 (0-100)
    var efficiencyScore: Double {
        let baseScore = Double(pattern.score)

        // 根据使用时长调整评分
        let durationFactor: Double
        switch duration {
        case 0..<300:       // 5分钟以下
            durationFactor = 0.5
        case 300..<1800:    // 5-30分钟
            durationFactor = 1.0
        case 1800..<3600:   // 30分钟-1小时
            durationFactor = 0.8
        default:            // 超过1小时
            durationFactor = 0.6
        }

        return baseScore * durationFactor
    }

    /// 格式化的使用时长
    var formattedDuration: String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else if minutes > 0 {
            return "\(minutes)m \(seconds)s"
        } else {
            return "\(seconds)s"
        }
    }

    /// 生成分析报告
    var analysisReport: String {
        var report = "📱 应用分析报告\n"
        report += "应用名称: \(appName)\n"
        report += "使用时长: \(formattedDuration)\n"
        report += "使用模式: \(pattern.pattern)\n"
        report += "效率评分: \(String(format: "%.1f", efficiencyScore))/100\n"

        if let avg = averageMetrics {
            report += "\n📊 平均资源使用:\n"
            report += "• CPU: \(String(format: "%.1f", avg.avgCPU))%\n"
            report += "• 内存: \(String(format: "%.1f", avg.avgMemory))MB\n"
            report += "• 磁盘: \(String(format: "%.1f", avg.avgDisk))%\n"
        }

        if let peak = peakMetrics {
            report += "\n🔥 峰值资源使用:\n"
            report += "• CPU峰值: \(String(format: "%.1f", peak.peakCPU))%\n"
            report += "• 内存峰值: \(peak.peakMemory)MB\n"
            report += "• 磁盘峰值: \(String(format: "%.1f", peak.peakDisk))%\n"
        }

        if !pattern.recommendations.isEmpty {
            report += "\n💡 建议:\n"
            for recommendation in pattern.recommendations {
                report += "• \(recommendation)\n"
            }
        }

        return report
    }

    // 为了支持Codable，需要自定义编码/解码
    enum CodingKeys: String, CodingKey {
        case appName, bundleID, duration, pattern, analysisTime
        case avgCPU, avgMemory, avgDisk, avgNetworkActivity
        case peakCPU, peakMemory, peakDisk, peakNetworkActivity
    }

    init(appName: String, bundleID: String, duration: TimeInterval, pattern: AppUsagePattern,
         averageMetrics: (avgCPU: Double, avgMemory: Double, avgDisk: Double, avgNetworkActivity: Double)?,
         peakMetrics: (peakCPU: Double, peakMemory: Int, peakDisk: Double, peakNetworkActivity: Int64)?,
         analysisTime: Date) {
        self.appName = appName
        self.bundleID = bundleID
        self.duration = duration
        self.pattern = pattern
        self.averageMetrics = averageMetrics
        self.peakMetrics = peakMetrics
        self.analysisTime = analysisTime
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        appName = try container.decode(String.self, forKey: .appName)
        bundleID = try container.decode(String.self, forKey: .bundleID)
        duration = try container.decode(TimeInterval.self, forKey: .duration)
        pattern = try container.decode(AppUsagePattern.self, forKey: .pattern)
        analysisTime = try container.decode(Date.self, forKey: .analysisTime)

        // 解码可选的元组数据
        if container.contains(.avgCPU) {
            let avgCPU = try container.decode(Double.self, forKey: .avgCPU)
            let avgMemory = try container.decode(Double.self, forKey: .avgMemory)
            let avgDisk = try container.decode(Double.self, forKey: .avgDisk)
            let avgNetworkActivity = try container.decode(Double.self, forKey: .avgNetworkActivity)
            averageMetrics = (avgCPU, avgMemory, avgDisk, avgNetworkActivity)
        } else {
            averageMetrics = nil
        }

        if container.contains(.peakCPU) {
            let peakCPU = try container.decode(Double.self, forKey: .peakCPU)
            let peakMemory = try container.decode(Int.self, forKey: .peakMemory)
            let peakDisk = try container.decode(Double.self, forKey: .peakDisk)
            let peakNetworkActivity = try container.decode(Int64.self, forKey: .peakNetworkActivity)
            peakMetrics = (peakCPU, peakMemory, peakDisk, peakNetworkActivity)
        } else {
            peakMetrics = nil
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(appName, forKey: .appName)
        try container.encode(bundleID, forKey: .bundleID)
        try container.encode(duration, forKey: .duration)
        try container.encode(pattern, forKey: .pattern)
        try container.encode(analysisTime, forKey: .analysisTime)

        // 编码可选的元组数据
        if let avg = averageMetrics {
            try container.encode(avg.avgCPU, forKey: .avgCPU)
            try container.encode(avg.avgMemory, forKey: .avgMemory)
            try container.encode(avg.avgDisk, forKey: .avgDisk)
            try container.encode(avg.avgNetworkActivity, forKey: .avgNetworkActivity)
        }

        if let peak = peakMetrics {
            try container.encode(peak.peakCPU, forKey: .peakCPU)
            try container.encode(peak.peakMemory, forKey: .peakMemory)
            try container.encode(peak.peakDisk, forKey: .peakDisk)
            try container.encode(peak.peakNetworkActivity, forKey: .peakNetworkActivity)
        }
    }
}

/// 监控性能统计模型
struct MonitoringPerformanceStats: Codable {
    let totalDataPoints: Int
    let lastCollectionTime: Date?
    let isMonitoring: Bool
    let databaseSize: Int64
    let activeAppsCount: Int
    let currentAppUsage: AppUsageRecord?
    let collectionInterval: TimeInterval
    let memoryUsage: Int              // KB
    let cpuImpact: Double            // 百分比

    /// 格式化的内存使用量
    var formattedMemoryUsage: String {
        if memoryUsage < 1024 {
            return "\(memoryUsage)KB"
        } else {
            return String(format: "%.1fMB", Double(memoryUsage) / 1024.0)
        }
    }

    /// 格式化的数据库大小
    var formattedDatabaseSize: String {
        ByteCountFormatter.string(fromByteCount: databaseSize, countStyle: .file)
    }

    /// 监控效率评分 (0-100)
    var efficiencyScore: Double {
        var score = 100.0

        // CPU影响评分
        if cpuImpact > 2.0 {
            score -= (cpuImpact - 2.0) * 10
        }

        // 内存使用评分
        if memoryUsage > 10240 { // 10MB
            score -= Double(memoryUsage - 10240) / 1024.0 * 5
        }

        // 采集频率评分
        if collectionInterval < 3.0 {
            score -= (3.0 - collectionInterval) * 10
        }

        return max(0, min(100, score))
    }

    /// 性能状态
    var performanceStatus: String {
        let score = efficiencyScore
        switch score {
        case 90...100:
            return "优秀"
        case 70..<90:
            return "良好"
        case 50..<70:
            return "一般"
        case 30..<50:
            return "较差"
        default:
            return "需要优化"
        }
    }

    /// 生成性能报告
    var performanceReport: String {
        var report = "📊 监控性能报告\n"
        report += "状态: \(isMonitoring ? "运行中" : "已停止")\n"
        report += "性能评分: \(String(format: "%.1f", efficiencyScore))/100 (\(performanceStatus))\n"
        report += "数据点总数: \(totalDataPoints)\n"
        report += "采集间隔: \(String(format: "%.1f", collectionInterval))秒\n"
        report += "内存使用: \(formattedMemoryUsage)\n"
        report += "CPU影响: \(String(format: "%.2f", cpuImpact))%\n"
        report += "数据库大小: \(formattedDatabaseSize)\n"
        report += "活跃应用数: \(activeAppsCount)\n"

        if let lastTime = lastCollectionTime {
            let formatter = DateFormatter()
            formatter.dateStyle = .none
            formatter.timeStyle = .medium
            report += "最后采集: \(formatter.string(from: lastTime))\n"
        }

        if let currentApp = currentAppUsage {
            report += "当前跟踪: \(currentApp.appName) (\(String(format: "%.1f", currentApp.duration))秒)\n"
        }

        return report
    }
}
