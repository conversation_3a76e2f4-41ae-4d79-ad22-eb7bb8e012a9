//
//  OptimizedDatabaseModels.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  优化数据库架构对应的数据模型
//

import Foundation

// MARK: - 应用信息模型

/// 应用信息模型
struct AppInfo: Codable, Identifiable {
    let id: Int64
    let bundleID: String
    let appName: String
    let appVersion: String?
    let appCategory: String?
    let vendorName: String?
    let installDate: Date?
    let lastUpdated: Date?
    let isSystemApp: Bool
    let createdAt: Date
    let updatedAt: Date
    
    init(id: Int64, bundleID: String, appName: String, appVersion: String? = nil, 
         appCategory: String? = nil, vendorName: String? = nil, installDate: Date? = nil,
         lastUpdated: Date? = nil, isSystemApp: Bool = false, 
         createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.bundleID = bundleID
        self.appName = appName
        self.appVersion = appVersion
        self.appCategory = appCategory
        self.vendorName = vendorName
        self.installDate = installDate
        self.lastUpdated = lastUpdated
        self.isSystemApp = isSystemApp
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - 系统指标模型

/// CPU指标模型
struct CPUMetricsModel: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let usagePercent: Double        // 0.0-100.0
    let userPercent: Double
    let systemPercent: Double
    let idlePercent: Double
    let loadAvg1min: Double?
    let loadAvg5min: Double?
    let loadAvg15min: Double?
    let coreCount: Int
    let frequencyMHz: Int?
    let createdAt: Date
    
    init(timestamp: Date = Date(), usagePercent: Double, userPercent: Double, 
         systemPercent: Double, idlePercent: Double, loadAvg1min: Double? = nil,
         loadAvg5min: Double? = nil, loadAvg15min: Double? = nil, coreCount: Int,
         frequencyMHz: Int? = nil) {
        self.id = nil
        self.timestamp = timestamp
        self.usagePercent = usagePercent
        self.userPercent = userPercent
        self.systemPercent = systemPercent
        self.idlePercent = idlePercent
        self.loadAvg1min = loadAvg1min
        self.loadAvg5min = loadAvg5min
        self.loadAvg15min = loadAvg15min
        self.coreCount = coreCount
        self.frequencyMHz = frequencyMHz
        self.createdAt = Date()
    }
}

/// 内存指标模型
struct MemoryMetricsModel: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let usagePercent: Double        // 0.0-100.0
    let usedMB: Int
    let availableMB: Int
    let totalMB: Int
    let activeMB: Int
    let inactiveMB: Int
    let wiredMB: Int
    let compressedMB: Int
    let swapUsedMB: Int
    let swapTotalMB: Int
    let memoryPressure: Int         // 0-100
    let createdAt: Date
    
    init(timestamp: Date = Date(), usagePercent: Double, usedMB: Int, availableMB: Int,
         totalMB: Int, activeMB: Int, inactiveMB: Int, wiredMB: Int, compressedMB: Int,
         swapUsedMB: Int, swapTotalMB: Int, memoryPressure: Int) {
        self.id = nil
        self.timestamp = timestamp
        self.usagePercent = usagePercent
        self.usedMB = usedMB
        self.availableMB = availableMB
        self.totalMB = totalMB
        self.activeMB = activeMB
        self.inactiveMB = inactiveMB
        self.wiredMB = wiredMB
        self.compressedMB = compressedMB
        self.swapUsedMB = swapUsedMB
        self.swapTotalMB = swapTotalMB
        self.memoryPressure = memoryPressure
        self.createdAt = Date()
    }
}

/// 磁盘指标模型
struct DiskMetricsModel: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let usagePercent: Double        // 0.0-100.0
    let usedGB: Int
    let freeGB: Int
    let totalGB: Int
    let readOpsPerSec: Int?
    let writeOpsPerSec: Int?
    let readBytesPerSec: Int?
    let writeBytesPerSec: Int?
    let createdAt: Date
    
    init(timestamp: Date = Date(), usagePercent: Double, usedGB: Int, freeGB: Int,
         totalGB: Int, readOpsPerSec: Int? = nil, writeOpsPerSec: Int? = nil,
         readBytesPerSec: Int? = nil, writeBytesPerSec: Int? = nil) {
        self.id = nil
        self.timestamp = timestamp
        self.usagePercent = usagePercent
        self.usedGB = usedGB
        self.freeGB = freeGB
        self.totalGB = totalGB
        self.readOpsPerSec = readOpsPerSec
        self.writeOpsPerSec = writeOpsPerSec
        self.readBytesPerSec = readBytesPerSec
        self.writeBytesPerSec = writeBytesPerSec
        self.createdAt = Date()
    }
}

/// 电池指标模型
struct BatteryMetricsModel: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let levelPercent: Int?          // 0-100
    let healthPercent: Int?         // 0-100
    let cycleCount: Int?
    let powerSourceType: String     // Battery, AC Power, UPS
    let isCharging: Bool
    let timeRemainingMinutes: Int?
    let temperatureCelsius: Double?
    let voltageMV: Int?             // 电压（毫伏）
    let createdAt: Date
    
    init(timestamp: Date = Date(), levelPercent: Int? = nil, healthPercent: Int? = nil,
         cycleCount: Int? = nil, powerSourceType: String, isCharging: Bool = false,
         timeRemainingMinutes: Int? = nil, temperatureCelsius: Double? = nil,
         voltageMV: Int? = nil) {
        self.id = nil
        self.timestamp = timestamp
        self.levelPercent = levelPercent
        self.healthPercent = healthPercent
        self.cycleCount = cycleCount
        self.powerSourceType = powerSourceType
        self.isCharging = isCharging
        self.timeRemainingMinutes = timeRemainingMinutes
        self.temperatureCelsius = temperatureCelsius
        self.voltageMV = voltageMV
        self.createdAt = Date()
    }
}

/// 温度指标模型
struct ThermalMetricsModel: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let cpuTemperatureCelsius: Double?
    let gpuTemperatureCelsius: Double?
    let fanSpeedRPM: Int?
    let thermalState: String?       // Normal, Fair, Serious, Critical
    let createdAt: Date
    
    init(timestamp: Date = Date(), cpuTemperatureCelsius: Double? = nil,
         gpuTemperatureCelsius: Double? = nil, fanSpeedRPM: Int? = nil,
         thermalState: String? = nil) {
        self.id = nil
        self.timestamp = timestamp
        self.cpuTemperatureCelsius = cpuTemperatureCelsius
        self.gpuTemperatureCelsius = gpuTemperatureCelsius
        self.fanSpeedRPM = fanSpeedRPM
        self.thermalState = thermalState
        self.createdAt = Date()
    }
}

/// 网络指标模型
struct NetworkMetricsModel: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let bytesIn: Int64
    let bytesOut: Int64
    let packetsIn: Int64
    let packetsOut: Int64
    let errorsIn: Int
    let errorsOut: Int
    let interfaceName: String?
    let createdAt: Date
    
    init(timestamp: Date = Date(), bytesIn: Int64, bytesOut: Int64,
         packetsIn: Int64, packetsOut: Int64, errorsIn: Int = 0, errorsOut: Int = 0,
         interfaceName: String? = nil) {
        self.id = nil
        self.timestamp = timestamp
        self.bytesIn = bytesIn
        self.bytesOut = bytesOut
        self.packetsIn = packetsIn
        self.packetsOut = packetsOut
        self.errorsIn = errorsIn
        self.errorsOut = errorsOut
        self.interfaceName = interfaceName
        self.createdAt = Date()
    }
}

// MARK: - 进程和应用使用模型

/// 进程快照模型
struct ProcessSnapshot: Codable, Identifiable {
    let id: Int64?
    let timestamp: Date
    let pid: Int32
    let processName: String
    let cpuUsagePercent: Double     // 0.0-100.0
    let memoryUsageMB: Int
    let appID: Int64?               // 关联的应用ID
    let parentPID: Int32?
    let createdAt: Date
    
    init(timestamp: Date = Date(), pid: Int32, processName: String,
         cpuUsagePercent: Double, memoryUsageMB: Int, appID: Int64? = nil,
         parentPID: Int32? = nil) {
        self.id = nil
        self.timestamp = timestamp
        self.pid = pid
        self.processName = processName
        self.cpuUsagePercent = cpuUsagePercent
        self.memoryUsageMB = memoryUsageMB
        self.appID = appID
        self.parentPID = parentPID
        self.createdAt = Date()
    }
}

/// 应用使用会话模型
struct AppUsageSession: Codable, Identifiable {
    let id: Int64?
    let appID: Int64
    let sessionStart: Date
    var sessionEnd: Date?
    var durationSeconds: Int
    var peakCPUUsage: Double        // 0.0-100.0
    var avgCPUUsage: Double         // 0.0-100.0
    var peakMemoryMB: Int
    var avgMemoryMB: Int
    var switchCount: Int            // 切换次数
    var isActive: Bool
    let createdAt: Date
    var updatedAt: Date
    
    init(appID: Int64, sessionStart: Date = Date()) {
        self.id = nil
        self.appID = appID
        self.sessionStart = sessionStart
        self.sessionEnd = nil
        self.durationSeconds = 0
        self.peakCPUUsage = 0.0
        self.avgCPUUsage = 0.0
        self.peakMemoryMB = 0
        self.avgMemoryMB = 0
        self.switchCount = 1
        self.isActive = true
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    init(id: Int64?, appID: Int64, sessionStart: Date, sessionEnd: Date?,
         durationSeconds: Int, peakCPUUsage: Double, avgCPUUsage: Double,
         peakMemoryMB: Int, avgMemoryMB: Int, switchCount: Int, isActive: Bool,
         createdAt: Date, updatedAt: Date) {
        self.id = id
        self.appID = appID
        self.sessionStart = sessionStart
        self.sessionEnd = sessionEnd
        self.durationSeconds = durationSeconds
        self.peakCPUUsage = peakCPUUsage
        self.avgCPUUsage = avgCPUUsage
        self.peakMemoryMB = peakMemoryMB
        self.avgMemoryMB = avgMemoryMB
        self.switchCount = switchCount
        self.isActive = isActive
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
    
    /// 结束会话
    mutating func endSession() {
        self.sessionEnd = Date()
        self.isActive = false
        if let end = sessionEnd {
            self.durationSeconds = Int(end.timeIntervalSince(sessionStart))
        }
        self.updatedAt = Date()
    }
    
    /// 更新CPU使用率
    mutating func updateCPUUsage(_ usage: Double) {
        self.peakCPUUsage = max(self.peakCPUUsage, usage)
        // 简单的移动平均
        self.avgCPUUsage = (self.avgCPUUsage + usage) / 2.0
        self.updatedAt = Date()
    }
    
    /// 更新内存使用量
    mutating func updateMemoryUsage(_ usageMB: Int) {
        self.peakMemoryMB = max(self.peakMemoryMB, usageMB)
        // 简单的移动平均
        self.avgMemoryMB = (self.avgMemoryMB + usageMB) / 2
        self.updatedAt = Date()
    }
}

/// 应用使用详细数据模型
struct AppUsageDetail: Codable, Identifiable {
    let id: Int64?
    let sessionID: Int64
    let timestamp: Date
    let cpuUsage: Double            // 0.0-100.0
    let memoryUsageMB: Int
    let createdAt: Date
    
    init(sessionID: Int64, timestamp: Date = Date(), cpuUsage: Double, memoryUsageMB: Int) {
        self.id = nil
        self.sessionID = sessionID
        self.timestamp = timestamp
        self.cpuUsage = cpuUsage
        self.memoryUsageMB = memoryUsageMB
        self.createdAt = Date()
    }
}

// MARK: - 统计和分析模型

/// 应用使用统计模型
struct AppUsageStats: Codable, Identifiable {
    let id = UUID()
    let bundleID: String
    let appName: String
    let appCategory: String?
    let sessionCount: Int
    let totalDurationSeconds: Int
    let avgDurationSeconds: Double
    let maxCPUUsage: Double         // 0.0-100.0
    let avgCPUUsage: Double         // 0.0-100.0
    let maxMemoryMB: Int
    let avgMemoryMB: Double
    let firstUsed: Date
    let lastUsed: Date

    /// 格式化的总使用时长
    var formattedTotalDuration: String {
        let hours = totalDurationSeconds / 3600
        let minutes = (totalDurationSeconds % 3600) / 60
        return "\(hours)h \(minutes)m"
    }

    /// 格式化的平均使用时长
    var formattedAvgDuration: String {
        let minutes = Int(avgDurationSeconds) / 60
        let seconds = Int(avgDurationSeconds) % 60
        return "\(minutes)m \(seconds)s"
    }
}


/// 系统事件模型
struct SystemEvent: Codable, Identifiable {
    let id: Int64?
    let eventType: String           // startup, shutdown, sleep, wake, app_launch, app_quit
    let eventTimestamp: Date
    let appID: Int64?
    let additionalData: String?     // JSON格式的额外数据
    let createdAt: Date

    init(eventType: String, eventTimestamp: Date = Date(), appID: Int64? = nil, additionalData: String? = nil) {
        self.id = nil
        self.eventType = eventType
        self.eventTimestamp = eventTimestamp
        self.appID = appID
        self.additionalData = additionalData
        self.createdAt = Date()
    }
}

/// 配置项模型
struct ConfigItem: Codable, Identifiable {
    let id: Int64?
    let configCategory: String
    let configKey: String
    let configValue: String
    let valueType: String           // string, integer, boolean, json
    let description: String?
    let isUserConfigurable: Bool
    let createdAt: Date
    let updatedAt: Date

    init(category: String, key: String, value: String, valueType: String,
         description: String? = nil, isUserConfigurable: Bool = true) {
        self.id = nil
        self.configCategory = category
        self.configKey = key
        self.configValue = value
        self.valueType = valueType
        self.description = description
        self.isUserConfigurable = isUserConfigurable
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    /// 获取布尔值
    var boolValue: Bool? {
        guard valueType == "boolean" else { return nil }
        return configValue.lowercased() == "true"
    }

    /// 获取整数值
    var intValue: Int? {
        guard valueType == "integer" else { return nil }
        return Int(configValue)
    }

    /// 获取双精度值
    var doubleValue: Double? {
        guard valueType == "integer" || valueType == "double" else { return nil }
        return Double(configValue)
    }
}

// MARK: - 高性能查询结果模型

/// 应用使用趋势模型
struct AppUsageTrend: Codable, Identifiable {
    let id = UUID()
    let bundleID: String
    let appName: String
    let totalDuration: Int          // 总使用时长（秒）
    let activeDays: Int             // 活跃天数
    var dailyData: [DailyData]      // 每日数据

    /// 每日使用数据
    struct DailyData: Codable {
        let date: String            // YYYY-MM-DD格式
        let duration: Int           // 当日使用时长（秒）
        let sessions: Int           // 当日会话数
        let avgCPU: Double          // 当日平均CPU使用率
        let avgMemory: Double       // 当日平均内存使用量
    }

    /// 平均每日使用时长
    var avgDailyDuration: Double {
        guard activeDays > 0 else { return 0 }
        return Double(totalDuration) / Double(activeDays)
    }

    /// 使用趋势（增长、稳定、下降）
    var trend: String {
        guard dailyData.count >= 3 else { return "数据不足" }

        let recent = Array(dailyData.suffix(3))
        let durations = recent.map { $0.duration }

        if durations[2] > durations[1] && durations[1] > durations[0] {
            return "增长"
        } else if durations[2] < durations[1] && durations[1] < durations[0] {
            return "下降"
        } else {
            return "稳定"
        }
    }
}

/// 资源使用峰值模型
struct ResourceUsagePeaks: Codable {
    var cpuPeak: Peak?
    var memoryPeak: Peak?
    var diskPeak: Peak?
    var temperaturePeak: Peak?

    /// 峰值数据
    struct Peak: Codable {
        let timestamp: Date
        let value: Double
        let additionalInfo: [String: Double]

        /// 格式化的时间
        var formattedTime: String {
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            formatter.timeStyle = .medium
            return formatter.string(from: timestamp)
        }
    }

    /// 是否有任何峰值数据
    var hasAnyPeak: Bool {
        return cpuPeak != nil || memoryPeak != nil || diskPeak != nil || temperaturePeak != nil
    }
}

/// 系统性能报告模型
struct SystemPerformanceReport: Codable {
    let reportPeriod: String
    let startDate: Date
    let endDate: Date
    let averageMetrics: SystemPerformanceOverview
    let peakCPU: Double
    let peakMemory: Double
    let peakTemperature: Double?
    let topApps: [AppUsageStats]
    let databaseSize: DatabaseSizeInfo?
    let generatedAt: Date

    /// 系统健康评分
    var healthScore: Double {
        return averageMetrics.healthScore
    }

    /// 健康状态描述
    var healthStatus: String {
        return averageMetrics.healthStatus
    }

    /// 格式化的报告期间
    var formattedPeriod: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"
    }

    /// 性能趋势分析
    var performanceTrend: String {
        // 基于平均CPU和内存使用率判断趋势
        let avgCPU = averageMetrics.cpuUsage ?? 0
        let avgMemory = averageMetrics.memoryUsage ?? 0

        let overallUsage = (avgCPU + avgMemory) / 2

        switch overallUsage {
        case 0..<0.3:
            return "轻度使用"
        case 0.3..<0.6:
            return "中度使用"
        case 0.6..<0.8:
            return "重度使用"
        default:
            return "高负载"
        }
    }

    /// 生成报告摘要
    var summary: String {
        var summary = "📊 系统性能报告 (\(reportPeriod))\n"
        summary += "📅 报告期间: \(formattedPeriod)\n"
        summary += "🏥 健康状态: \(healthStatus) (\(String(format: "%.1f", healthScore))分)\n"
        summary += "📈 使用趋势: \(performanceTrend)\n\n"

        if let cpu = averageMetrics.cpuUsage {
            summary += "🖥️ 平均CPU使用率: \(String(format: "%.1f", cpu * 100))%\n"
        }

        if let memory = averageMetrics.memoryUsage {
            summary += "💾 平均内存使用率: \(String(format: "%.1f", memory * 100))%\n"
        }

        if let temp = averageMetrics.cpuTemperature {
            summary += "🌡️ 平均CPU温度: \(String(format: "%.1f", temp))°C\n"
        }

        summary += "\n🔥 峰值数据:\n"
        summary += "• CPU峰值: \(String(format: "%.1f", peakCPU * 100))%\n"
        summary += "• 内存峰值: \(String(format: "%.1f", peakMemory * 100))%\n"

        if let peakTemp = peakTemperature {
            summary += "• 温度峰值: \(String(format: "%.1f", peakTemp))°C\n"
        }

        if !topApps.isEmpty {
            summary += "\n📱 最常用应用:\n"
            for (index, app) in topApps.enumerated() {
                summary += "\(index + 1). \(app.appName) - \(app.formattedTotalDuration)\n"
            }
        }

        if let dbSize = databaseSize {
            summary += "\n💾 数据库信息:\n"
            summary += "• 数据库大小: \(dbSize.formattedTotalSize)\n"
            summary += "• 总记录数: \(dbSize.totalRecords)\n"
        }

        return summary
    }
}

/// 数据库查询性能统计
struct QueryPerformanceStats: Codable {
    let queryType: String
    let executionTimeMs: Double
    let rowsReturned: Int
    let timestamp: Date

    init(queryType: String, executionTimeMs: Double, rowsReturned: Int) {
        self.queryType = queryType
        self.executionTimeMs = executionTimeMs
        self.rowsReturned = rowsReturned
        self.timestamp = Date()
    }
}

/// 系统健康评估详细结果
struct DetailedSystemHealthAssessment: Codable {
    let overallScore: Double        // 0-100分
    let assessmentDate: Date

    // 各组件健康评分
    let cpuHealth: ComponentHealth
    let memoryHealth: ComponentHealth
    let diskHealth: ComponentHealth
    let batteryHealth: ComponentHealth?
    let thermalHealth: ComponentHealth

    // 建议和警告
    let recommendations: [String]
    let warnings: [String]
    let criticalIssues: [String]

    /// 组件健康状态
    struct ComponentHealth: Codable {
        let score: Double           // 0-100分
        let status: String          // 优秀、良好、一般、较差、危险
        let currentValue: Double    // 当前值
        let averageValue: Double    // 平均值
        let peakValue: Double       // 峰值
        let trend: String           // 趋势：改善、稳定、恶化
        let details: [String: Double] // 详细指标
    }

    /// 整体健康状态
    var healthStatus: String {
        switch overallScore {
        case 90...100:
            return "优秀"
        case 70..<90:
            return "良好"
        case 50..<70:
            return "一般"
        case 30..<50:
            return "较差"
        default:
            return "危险"
        }
    }

    /// 是否需要立即关注
    var needsImmediateAttention: Bool {
        return overallScore < 50 || !criticalIssues.isEmpty
    }
}

